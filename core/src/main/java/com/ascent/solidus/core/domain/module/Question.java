package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.Frequency;
import com.ascent.solidus.core.constants.InspectionLevel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "question"
)
public class Question {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "stage_id"
  )
  private Stage stage;

  @ManyToOne
  @JoinColumn(
      name = "auditArea_id"
  )
  private AuditArea auditArea;

  @ManyToOne
  @JoinColumn(
      name = "category_id"
  )
  private ProcessAuditCategory category;

  @ManyToOne
  @JoinColumn(
      name = "spotBuySubCategory_id"
  )
  private SpotBuySubCategory spotBuySubCategory;

  private Frequency frequency;

  private InspectionLevel inspectionlevel;

  @Transient
  private SamplingPlanLimit samplingPlanLimit;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public Stage getStage() {
    return this.stage;
  }

  public void setStage(Stage stage) {
    this.stage = stage;
  }

  public AuditArea getAuditArea() {
    return this.auditArea;
  }

  public void setAuditArea(AuditArea auditArea) {
    this.auditArea = auditArea;
  }

  public ProcessAuditCategory getCategory() {
    return this.category;
  }

  public void setCategory(ProcessAuditCategory category) {
    this.category = category;
  }

  public SpotBuySubCategory getSpotBuySubCategory() {
    return this.spotBuySubCategory;
  }

  public void setSpotBuySubCategory(SpotBuySubCategory spotBuySubCategory) {
    this.spotBuySubCategory = spotBuySubCategory;
  }

  @Enumerated(EnumType.STRING)
  public Frequency getFrequency() {
    return this.frequency;
  }

  public void setFrequency(Frequency frequency) {
    this.frequency = frequency;
  }

  @Enumerated(EnumType.STRING)
  public InspectionLevel getInspectionLevel() {
    return this.inspectionlevel;
  }

  public void setInspectionLevel(InspectionLevel inspectionlevel) {
    this.inspectionlevel = inspectionlevel;
  }

  public SamplingPlanLimit getSamplingPlanLimit() {
    return this.samplingPlanLimit;
  }

  public void setSamplingPlanLimit(SamplingPlanLimit samplingPlanLimit) {
    this.samplingPlanLimit = samplingPlanLimit;
  }
}
