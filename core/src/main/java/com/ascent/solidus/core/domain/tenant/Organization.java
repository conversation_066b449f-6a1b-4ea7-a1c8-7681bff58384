package com.ascent.solidus.core.domain.tenant;

import com.ascent.solidus.core.constants.EnvironmentType;
import com.ascent.solidus.core.domain.AbstractEntity;
import com.ascent.solidus.core.domain.umgmt.City;
import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.domain.umgmt.State;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.Date;

@Entity
@Table(name = "organizations")
public class Organization extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(nullable = false)
    private String name;

    private String contactPersonName;

    @NotBlank
    @Size(max = 100)
    @Email
    @Column(nullable = false, unique = true)
    private String email;

    @Size(max = 20)
    private String phone;

    private String otherContactNumber;

    private String addressLine1;

    private String addressLine2;

    private String area;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "City_Id", referencedColumnName = "Id")
    private City city;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "State_Id", referencedColumnName = "Id")
    private State state;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "Country_Id", referencedColumnName = "Id")
    private Country country;

    private String zipCode;

    private EnvironmentType environmentType;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOtherContactNumber() {
        return otherContactNumber;
    }

    public void setOtherContactNumber(String otherContactNumber) {
        this.otherContactNumber = otherContactNumber;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public EnvironmentType getEnvironmentType() {
        return environmentType;
    }

    public void setEnvironmentType(EnvironmentType environmentType) {
        this.environmentType = environmentType;
    }

    public String getThemeColor() {
        return themeColor;
    }

    public void setThemeColor(String themeColor) {
        this.themeColor = themeColor;
    }

    public String getLogoPath() {
        return logoPath;
    }

    public void setLogoPath(String logoPath) {
        this.logoPath = logoPath;
    }

    @Override
    public Tenant getTenant() {
        return tenant;
    }

    @Override
    public void setTenant(Tenant tenant) {
        this.tenant = tenant;
    }

    private String themeColor;

    private String logoPath;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "tenant_id")
    private Tenant tenant;
}
