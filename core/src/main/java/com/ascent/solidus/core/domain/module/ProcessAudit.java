package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.AuditType;
import com.ascent.solidus.core.constants.Frequency;
import com.ascent.solidus.core.constants.Status;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "processaudit"
)
public class ProcessAudit {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private Status status;

  @ManyToOne
  @JoinColumn(
      name = "auditBy_id"
  )
  private AppUser auditBy;

  @ManyToOne
  @JoinColumn(
      name = "assignAuditArea_id"
  )
  private AssignAuditArea assignAuditArea;

  @ManyToOne
  @JoinColumn(
      name = "factory_id"
  )
  private Factory factory;

  @ManyToOne
  @JoinColumn(
      name = "sector_id"
  )
  private Sector sector;

  @ManyToOne
  @JoinColumn(
      name = "auditArea_id"
  )
  private AuditArea auditArea;

  private Frequency frequency;

  private AuditType audittype;

  @ManyToOne
  @JoinColumn(
      name = "productLabel_id"
  )
  private ProductLabel productLabel;

  @ManyToOne
  @JoinColumn(
      name = "assemblyLine_id"
  )
  private AssemblyLine assemblyLine;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public Status getStatus() {
    return this.status;
  }

  public void setStatus(Status status) {
    this.status = status;
  }

  public AppUser getAuditBy() {
    return this.auditBy;
  }

  public void setAuditBy(AppUser auditBy) {
    this.auditBy = auditBy;
  }

  public AssignAuditArea getAssignAuditArea() {
    return this.assignAuditArea;
  }

  public void setAssignAuditArea(AssignAuditArea assignAuditArea) {
    this.assignAuditArea = assignAuditArea;
  }

  public Factory getFactory() {
    return this.factory;
  }

  public void setFactory(Factory factory) {
    this.factory = factory;
  }

  public Sector getSector() {
    return this.sector;
  }

  public void setSector(Sector sector) {
    this.sector = sector;
  }

  public AuditArea getAuditArea() {
    return this.auditArea;
  }

  public void setAuditArea(AuditArea auditArea) {
    this.auditArea = auditArea;
  }

  @Enumerated(EnumType.STRING)
  public Frequency getFrequency() {
    return this.frequency;
  }

  public void setFrequency(Frequency frequency) {
    this.frequency = frequency;
  }

  @Enumerated(EnumType.STRING)
  public AuditType getAuditType() {
    return this.audittype;
  }

  public void setAuditType(AuditType audittype) {
    this.audittype = audittype;
  }

  public ProductLabel getProductLabel() {
    return this.productLabel;
  }

  public void setProductLabel(ProductLabel productLabel) {
    this.productLabel = productLabel;
  }

  public AssemblyLine getAssemblyLine() {
    return this.assemblyLine;
  }

  public void setAssemblyLine(AssemblyLine assemblyLine) {
    this.assemblyLine = assemblyLine;
  }
}
