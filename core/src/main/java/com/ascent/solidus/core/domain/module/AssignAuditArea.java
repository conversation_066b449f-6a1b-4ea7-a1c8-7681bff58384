package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import java.lang.String;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assignauditarea"
)
public class AssignAuditArea {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "auditArea_id"
  )
  private AuditArea auditArea;

  @Valid
  @Transient
  private List<AppUser> inspectors;

  AssignAuditArea() {
    this.inspectors = new ArrayList<>();
  }

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public AuditArea getAuditArea() {
    return this.auditArea;
  }

  public void setAuditArea(AuditArea auditArea) {
    this.auditArea = auditArea;
  }

  public List<AppUser> getInspectors() {
    return this.inspectors;
  }

  public void setInspectors(List<AppUser> inspectors) {
    this.inspectors = inspectors;
  }
}
