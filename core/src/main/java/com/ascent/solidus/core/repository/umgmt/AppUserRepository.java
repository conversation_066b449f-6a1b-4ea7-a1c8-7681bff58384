package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.module.Factory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AppUserRepository extends JpaRepository<AppUser, Long> {
    Optional<AppUser> findByUsernameAndActive(String username, boolean active);
    Optional<AppUser> findByEmailAndActive(String email, boolean active);
    List<AppUser> findByTenantAndActive(Tenant tenant, boolean active);
    List<AppUser> findByOrganizationAndActive(Organization organization, boolean active);
    List<AppUser> findByFactory(Factory existing);
}