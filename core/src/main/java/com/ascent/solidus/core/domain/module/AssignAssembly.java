package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assignassembly"
)
public class AssignAssembly {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id",
      referencedColumnName = "id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "product_id",
      referencedColumnName = "id"
  )
  private Product product;

  @ManyToOne
  @JoinColumn(
      name = "assemblyInspectionRound_id",
      referencedColumnName = "id"
  )
  private AssemblyInspectionRound assemblyInspectionRound;

  @ManyToOne
  @JoinColumn(
      name = "stage_id",
      referencedColumnName = "id"
  )
  private Stage stage;

  @Column(
      name = "name",
      nullable = true
  )
  private String name;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public Product getProduct() {
    return this.product;
  }

  public void setProduct(Product product) {
    this.product = product;
  }

  public AssemblyInspectionRound getAssemblyInspectionRound() {
    return this.assemblyInspectionRound;
  }

  public void setAssemblyInspectionRound(AssemblyInspectionRound assemblyInspectionRound) {
    this.assemblyInspectionRound = assemblyInspectionRound;
  }

  public Stage getStage() {
    return this.stage;
  }

  public void setStage(Stage stage) {
    this.stage = stage;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
