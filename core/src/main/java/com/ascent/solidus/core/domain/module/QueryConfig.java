package com.ascent.solidus.core.domain.module;

import java.util.List;

public class QueryConfig {
    private String name;
    private String entity;
    private String returnType;  // "List", "Set", "Map", or specific class name
    private String returnClass; // The class for collection types
    private List<JoinConfig> joins;
    private List<FilterConfig> filters;
    private List<String> groupBy;
    private List<AggregationConfig> aggregations;
    private List<HavingConfig> having;
    private List<SortConfig> sort;
    private List<String> selectFields; // For explicit field selection

    // With getter and setter
    public List<String> getSelectFields() {
        return selectFields;
    }

    public void setSelectFields(List<String> selectFields) {
        this.selectFields = selectFields;
    }

    // Constructors
    public QueryConfig() {}

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public String getReturnType() {
        return returnType;
    }

    public void setReturnType(String returnType) {
        this.returnType = returnType;
    }

    public String getReturnClass() {
        return returnClass;
    }

    public void setReturnClass(String returnClass) {
        this.returnClass = returnClass;
    }

    public List<JoinConfig> getJoins() {
        return joins;
    }

    public void setJoins(List<JoinConfig> joins) {
        this.joins = joins;
    }

    public List<FilterConfig> getFilters() {
        return filters;
    }

    public void setFilters(List<FilterConfig> filters) {
        this.filters = filters;
    }

    public List<String> getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(List<String> groupBy) {
        this.groupBy = groupBy;
    }

    public List<AggregationConfig> getAggregations() {
        return aggregations;
    }

    public void setAggregations(List<AggregationConfig> aggregations) {
        this.aggregations = aggregations;
    }

    public List<HavingConfig> getHaving() {
        return having;
    }

    public void setHaving(List<HavingConfig> having) {
        this.having = having;
    }

    public List<SortConfig> getSort() {
        return sort;
    }

    public void setSort(List<SortConfig> sort) {
        this.sort = sort;
    }


    @Override
    public String toString() {
        return "QueryConfig{" +
                "name='" + name + '\'' +
                ", entity='" + entity + '\'' +
                ", returnType='" + returnType + '\'' +
                ", returnClass='" + returnClass + '\'' +
                ", joins=" + joins +
                ", filters=" + filters +
                ", groupBy=" + groupBy +
                ", aggregations=" + aggregations +
                ", having=" + having +
                ", sort=" + sort +
                '}';
    }
}