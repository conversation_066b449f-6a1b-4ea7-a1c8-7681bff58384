package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.umgmt.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CountryRepository extends JpaRepository<Country, Long> {
    Optional<Country> findByName(String name);
    Optional<Country> findByNameAndActive(String name, boolean active);
}
