package com.ascent.solidus.core.domain.module;

import java.util.List;

public class ParentModuleDTO {
    private String moduleId;
    private String moduleName;
//    private String moduleIcon;
    private List<SubmoduleConfig> submodules;

    // Constructors
    public ParentModuleDTO() {}

    public ParentModuleDTO(String moduleId, String moduleName, List<SubmoduleConfig> submodules) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
//        this.moduleIcon = moduleIcon;
        this.submodules = submodules;
    }

    // Getters and Setters
    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
//
//    public String getModuleIcon() {
//        return moduleIcon;
//    }
//
//    public void setModuleIcon(String moduleIcon) {
//        this.moduleIcon = moduleIcon;
//    }

    public List<SubmoduleConfig> getSubmodules() {
        return submodules;
    }

    public void setSubmodules(List<SubmoduleConfig> submodules) {
        this.submodules = submodules;
    }
}
