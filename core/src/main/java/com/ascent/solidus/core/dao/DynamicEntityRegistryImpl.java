package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.squareup.javapoet.JavaFile;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.metamodel.Metamodel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import javax.tools.JavaCompiler;
import javax.tools.ToolProvider;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class DynamicEntityRegistryImpl implements DynamicEntityRegistry {
    private final Map<String, Class<?>> entityClasses = new ConcurrentHashMap<>();
    private final Map<String, ModuleConfig> moduleConfigs = new ConcurrentHashMap<>();
    private final DynamicEntityGenerator entityGenerator;
    private final DynamicRepositoryGenerator repositoryGenerator;
    private final DynamicServiceGenerator serviceGenerator;
    private final DynamicControllerGenerator controllerGenerator;


    private final EntityManagerFactory emf;

    @Autowired
    public DynamicEntityRegistryImpl(DynamicEntityGenerator entityGenerator, DynamicRepositoryGenerator repositoryGenerator, DynamicServiceGenerator serviceGenerator, DynamicControllerGenerator controllerGenerator,
                                     EntityManagerFactory emf) {
        this.entityGenerator = entityGenerator;
        this.repositoryGenerator = repositoryGenerator;
        this.serviceGenerator = serviceGenerator;
        this.controllerGenerator = controllerGenerator;
        this.emf = emf;
    }

    @Override
    public void registerEntity(ModuleConfig moduleConfig) throws IOException, ClassNotFoundException {
        try {
            // Generate and compile the entity class
            entityGenerator.generateEntityClass(moduleConfig);
            Path sourceDir = Paths.get("core/src/main/java");
            Class<?> entityClass = compileAndLoadClass(sourceDir, moduleConfig.getModuleName());

            // Store the loaded class
            entityClasses.put(moduleConfig.getModuleName(), entityClass);
            moduleConfigs.put(moduleConfig.getModuleName(), moduleConfig);

            // Generate other components
            repositoryGenerator.generateRepositoryInterface(entityClass, moduleConfig);
            serviceGenerator.generateServiceClass(entityClass, moduleConfig);
            controllerGenerator.generateControllerClass(entityClass, moduleConfig);

        } catch (Exception e) {
            throw new RuntimeException("Failed to register entity: " + moduleConfig.getModuleName(), e);
        }
    }

    private Class<?> compileAndLoadClass(Path sourceDir, String className) throws IOException, ClassNotFoundException {
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        if (compiler == null) {
            throw new IllegalStateException("No system Java compiler found. Are you running a JRE instead of a JDK?");
        }

        // Define paths
        Path javaFilePath = sourceDir.resolve("com/ascent/solidus/core/domain/module/" + className + ".java");
        Path outputDir = Paths.get("core/target/classes");

        // Ensure directories exist
        Files.createDirectories(outputDir);

        // Compile with proper classpath
        List<String> options = Arrays.asList(
                "-d", outputDir.toString(),
                "-classpath", System.getProperty("java.class.path"),
                javaFilePath.toString()
        );

        int result = compiler.run(null, null, null, options.toArray(new String[0]));

        if (result != 0) {
            throw new IOException("Compilation failed for: " + javaFilePath);
        }

        // Use a fresh classloader
        URLClassLoader classLoader = URLClassLoader.newInstance(
                new URL[]{outputDir.toUri().toURL()},
                Thread.currentThread().getContextClassLoader()
        );

        // Load the class
        return Class.forName("com.ascent.solidus.core.domain.module." + className, true, classLoader);
    }

//    private void compileJavaFile(Path sourceDir, String className) throws IOException, InterruptedException {
//        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
//        if (compiler == null) {
//            throw new IllegalStateException("No system Java compiler found. Are you running a JRE instead of a JDK?");
//        }
//
//        Path javaFilePath = sourceDir.resolve("com/ascent/solidus/core/domain/module/" + className + ".java");
//        System.out.println("Exists: " + Files.exists(javaFilePath));
//        Path outputDir = Paths.get("target/classes/com/ascent/solidus/core/domain/module"); // Make sure target/classes exists
//        Files.createDirectories(outputDir);
//
//        int result = compiler.run(null, null, null,
//                "-d", outputDir.toString(),    // <-- Specify output dir for .class
//                javaFilePath.toString());
//
//        Thread.sleep(1000);
//
//        if (result != 0) {
//            throw new IOException("Compilation failed for: " + javaFilePath);
//        }
//    }



    @Override
    public Class<?> getEntityClass(String entityName) {
        Class<?> clazz = entityClasses.get(entityName);
        if (clazz == null) {
            throw new IllegalArgumentException("Unknown entity type: " + entityName);
        }
        return clazz;
    }

    @Override
    public boolean isRegistered(String entityName) {
        return entityClasses.containsKey(entityName);
    }

    @Override
    public Set<String> getRegisteredEntities() {
        return entityClasses.keySet();
    }

    @Override
    public ModuleConfig getModuleConfig(String entityName) {
        return moduleConfigs.get(entityName);
    }
}