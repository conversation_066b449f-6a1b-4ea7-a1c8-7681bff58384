package com.ascent.solidus.core.domain.umgmt;

import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;


@Entity
@Table(name = "country")
public class Country extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
