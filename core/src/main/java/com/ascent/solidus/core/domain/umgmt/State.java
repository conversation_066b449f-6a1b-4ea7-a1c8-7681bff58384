package com.ascent.solidus.core.domain.umgmt;

import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;


@Entity
@Table(name = "state")
public class State extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 75)
    private String name;

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne
    @JoinColumn(name = "country_id", nullable = false)
    private Country country;
}
