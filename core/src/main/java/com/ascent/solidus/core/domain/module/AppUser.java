package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.domain.AbstractEntity;
import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.domain.umgmt.Role;
import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "app_users")
public class AppUser extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 50)
    @Column(nullable = false, unique = true)
    private String username;

    @NotBlank
    @Size(max = 100)
    @Email
    @Column(nullable = false, unique = true)
    private String email;

//    @NotBlank
    @Size(max = 120)
    @Column(nullable = false)
    private String password;

    @Size(max = 50)
    private String firstName;

    @Size(max = 50)
    private String lastName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "factory_id")
    private Factory factory;

    @Size(max = 20)
    private String phone;

    @Size(max = 200)
    private String address;

    @Size(max = 100)
    private String designation;

    @Size(max = 100)
    private String department;

    @Column(nullable = false)
    private boolean enabled = true;


    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Factory getFactory() {
        return factory;
    }

    public void setFactory(Factory factory) {
        this.factory = factory;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }

    public void setAccountNonLocked(boolean accountNonLocked) {
        this.accountNonLocked = accountNonLocked;
    }

    public Set<RoleAssignment> getAssignedRoles() {
        return assignedRoles;
    }

    public void setAssignedRoles(Set<RoleAssignment> assignedRoles) {
        this.assignedRoles = assignedRoles;
    }

    public List<Role> getRoles() {
        return roles;
    }

    public void setRoles(List<Role> roles) {
        this.roles = roles;
    }

    public String getAzureTenantId() {
        return azureTenantId;
    }

    public void setAzureTenantId(String azureTenantId) {
        this.azureTenantId = azureTenantId;
    }

    public String getAzureObjectId() {
        return azureObjectId;
    }

    public void setAzureObjectId(String azureObjectId) {
        this.azureObjectId = azureObjectId;
    }

    @Column(nullable = false)
    private boolean accountNonLocked = true;

    @OneToMany(mappedBy = "appUser", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<RoleAssignment> assignedRoles = new HashSet<>();

    @Transient
    private List<Role> roles;

    @Column(name = "azure_tid", length = 64)
    private String azureTenantId;

    @Column(name = "azure_oid", length = 64, unique = true)
    private String azureObjectId;

//    public String getAzureTenantId() {
//        return azureTenantId;
//    }
//
//    public void setAzureTenantId(String azureTenantId) {
//        this.azureTenantId = azureTenantId;
//    }
//
//    public String getAzureObjectId() {
//        return azureObjectId;
//    }
//
//    public void setAzureObjectId(String azureObjectId) {
//        this.azureObjectId = azureObjectId;
//    }
}
