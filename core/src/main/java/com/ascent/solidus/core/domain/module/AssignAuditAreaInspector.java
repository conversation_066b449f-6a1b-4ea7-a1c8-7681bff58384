package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assignauditareainspector"
)
public class AssignAuditAreaInspector {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "assignAuditArea_id"
  )
  private AssignAuditArea assignAuditArea;

  @ManyToOne
  @JoinColumn(
      name = "inspector_id"
  )
  private AppUser inspector;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public AssignAuditArea getAssignAuditArea() {
    return this.assignAuditArea;
  }

  public void setAssignAuditArea(AssignAuditArea assignAuditArea) {
    this.assignAuditArea = assignAuditArea;
  }

  public AppUser getInspector() {
    return this.inspector;
  }

  public void setInspector(AppUser inspector) {
    this.inspector = inspector;
  }
}
