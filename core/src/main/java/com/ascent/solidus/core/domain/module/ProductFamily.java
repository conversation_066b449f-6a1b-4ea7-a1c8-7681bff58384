package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "productfamily"
)
public class ProductFamily {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @Column(
      name = "name",
      nullable = false
  )
  private String name;

  @Column(
      name = "path",
      nullable = true
  )
  private String path;

  @Column(
      name = "description",
      nullable = true
  )
  private String description;

  @ManyToOne
  @JoinColumn(
      name = "productLabel_id",
      referencedColumnName = "id"
  )
  private ProductLabel productLabel;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getPath() {
    return this.path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public String getDescription() {
    return this.description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public ProductLabel getProductLabel() {
    return this.productLabel;
  }

  public void setProductLabel(ProductLabel productLabel) {
    this.productLabel = productLabel;
  }
}
