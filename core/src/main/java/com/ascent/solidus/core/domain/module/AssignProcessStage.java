package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.lang.String;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assignprocessstage"
)
public class AssignProcessStage {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @OneToMany
  @JoinTable(
      name = "inspectors_mapping"
  )
  private List<AppUser> inspectors;

  @ManyToOne
  @JoinColumn(
      name = "processStage_id"
  )
  private ProcessStage processStage;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public List<AppUser> getInspectors() {
    return this.inspectors;
  }

  public void setInspectors(List<AppUser> inspectors) {
    this.inspectors = inspectors;
  }

  public ProcessStage getProcessStage() {
    return this.processStage;
  }

  public void setProcessStage(ProcessStage processStage) {
    this.processStage = processStage;
  }
}
