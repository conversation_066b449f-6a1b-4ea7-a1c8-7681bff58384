package com.ascent.solidus.core.domain.umgmt;

import com.ascent.solidus.core.domain.AbstractEntity;
import com.ascent.solidus.core.domain.module.AppUser;
import jakarta.persistence.*;

@Entity
@Table(name = "app_user_mapping")
public class AppUserMapping extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supervisor_id", nullable = false)
    private AppUser supervisor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inspector_id", nullable = false)
    private AppUser inspector;
}