package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.FaultIntimationDecision;
import com.ascent.solidus.core.constants.FaultIntimationStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.lang.String;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "faultintimation"
)
public class FaultIntimation {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private FaultIntimationStatus faultintimationstatus;

  private FaultIntimationDecision faultintimationdecision;

  @ManyToOne
  @JoinColumn(
      name = "raisedBy_id"
  )
  private AppUser raisedBy;

  @ManyToOne
  @JoinColumn(
      name = "closedBy_id"
  )
  private AppUser closedBy;

  @ManyToOne
  @JoinColumn(
      name = "responsiblePerson_id"
  )
  private AppUser responsiblePerson;

  @ManyToOne
  @JoinColumn(
      name = "distributor_id"
  )
  private Distributor distributor;

  @OneToMany
  @JoinTable(
      name = "members_mapping"
  )
  private List<AppUser> members;

  @OneToMany
  @JoinTable(
      name = "defect_mapping"
  )
  private List<SpotBuyDefect> defect;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public FaultIntimationStatus getFaultIntimationStatus() {
    return this.faultintimationstatus;
  }

  public void setFaultIntimationStatus(FaultIntimationStatus faultintimationstatus) {
    this.faultintimationstatus = faultintimationstatus;
  }

  @Enumerated(EnumType.STRING)
  public FaultIntimationDecision getFaultIntimationDecision() {
    return this.faultintimationdecision;
  }

  public void setFaultIntimationDecision(FaultIntimationDecision faultintimationdecision) {
    this.faultintimationdecision = faultintimationdecision;
  }

  public AppUser getRaisedBy() {
    return this.raisedBy;
  }

  public void setRaisedBy(AppUser raisedBy) {
    this.raisedBy = raisedBy;
  }

  public AppUser getClosedBy() {
    return this.closedBy;
  }

  public void setClosedBy(AppUser closedBy) {
    this.closedBy = closedBy;
  }

  public AppUser getResponsiblePerson() {
    return this.responsiblePerson;
  }

  public void setResponsiblePerson(AppUser responsiblePerson) {
    this.responsiblePerson = responsiblePerson;
  }

  public Distributor getDistributor() {
    return this.distributor;
  }

  public void setDistributor(Distributor distributor) {
    this.distributor = distributor;
  }

  public List<AppUser> getMembers() {
    return this.members;
  }

  public void setMembers(List<AppUser> members) {
    this.members = members;
  }

  public List<SpotBuyDefect> getDefect() {
    return this.defect;
  }

  public void setDefect(List<SpotBuyDefect> defect) {
    this.defect = defect;
  }
}
