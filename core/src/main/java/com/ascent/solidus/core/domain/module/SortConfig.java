package com.ascent.solidus.core.domain.module;

public class SortConfig {
    private String field;
    private String direction;  // "asc", "desc"

    // Constructors
    public SortConfig() {}

    public SortConfig(String field, String direction) {
        this.field = field;
        this.direction = direction;
    }

    // Getters and Setters
    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    @Override
    public String toString() {
        return "SortConfig{" +
                "field='" + field + '\'' +
                ", direction='" + direction + '\'' +
                '}';
    }
}
