//package com.ascent.solidus.core.repository.module;
//
//import com.ascent.solidus.core.domain.module.Module;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//import java.util.Optional;
//
//@Repository
//public interface ModuleRepository extends JpaRepository<Module, Long> {
//    Optional<Module> findByName(String name);
//
//    List<Module> findByParentIsNull();
//
//    List<Module> findByParentId(Long parentId);
//
//    List<Module> findByIsStandaloneTrue();
//
//    List<Module> findByIsComponentTrue();
//
//    @Query("SELECT m FROM Module m WHERE m.isStandalone = true AND m.parent IS NULL")
//    List<Module> findAllTopLevelModules();
//}