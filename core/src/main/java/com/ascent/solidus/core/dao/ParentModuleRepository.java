package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.ParentModule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ParentModuleRepository extends JpaRepository<ParentModule, String> {
    // Custom query methods can be added here if needed
    ParentModule findByModuleName(String moduleName);

//    ParentModule findById(String id);
}
