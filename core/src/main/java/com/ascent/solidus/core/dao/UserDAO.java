package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.AppUser;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
@Transactional
public class UserDAO {

    private final EntityManager entityManager;

    public UserDAO(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public Optional<AppUser> findByUsername(String username) {
        TypedQuery<AppUser> query = entityManager.createQuery(
                "FROM AppUser WHERE username = :username", AppUser.class);
        query.setParameter("username", username);
        return query.getResultStream().findFirst();
    }

    public void save(AppUser user) {
        entityManager.persist(user);
    }
}