package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.domain.umgmt.State;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StateRepository extends JpaRepository<State, Long> {
    List<State> findByCountryId(Long countryId);
    List<State> findByCountry(Country country);
}
