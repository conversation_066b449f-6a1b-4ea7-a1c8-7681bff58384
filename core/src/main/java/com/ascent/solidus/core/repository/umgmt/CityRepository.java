package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.umgmt.City;
import com.ascent.solidus.core.domain.umgmt.State;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CityRepository extends JpaRepository<City, Long> {
    List<City> findByState(State state);
}