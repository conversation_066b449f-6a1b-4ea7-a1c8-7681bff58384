package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.lang.String;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "stagesubassemblyintegration"
)
public class StageSubAssemblyIntegration {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @OneToMany
  @JoinTable(
      name = "assemblies_mapping"
  )
  private List<Assembly> assemblies;

  @ManyToOne
  @JoinColumn(
      name = "stage_id"
  )
  private Stage stage;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id"
  )
  private Assembly assembly;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public List<Assembly> getAssemblies() {
    return this.assemblies;
  }

  public void setAssemblies(List<Assembly> assemblies) {
    this.assemblies = assemblies;
  }

  public Stage getStage() {
    return this.stage;
  }

  public void setStage(Stage stage) {
    this.stage = stage;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }
}
