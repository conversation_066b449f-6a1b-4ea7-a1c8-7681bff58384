package com.ascent.solidus.core.domain.module;

import java.util.List;



public class ModuleConfigRoot {
    private String moduleId;

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    private String moduleName;

    private List<ModuleConfig> modules;

    private String moduleType;

    private List<String> dependencies;

    private Boolean dependency;

    public List<QueryConfig> getQueryConfigs() {
        return queryConfigs;
    }

    public void setQueryConfigs(List<QueryConfig> queryConfigs) {
        this.queryConfigs = queryConfigs;
    }

    private List<QueryConfig> queryConfigs;


    public Boolean getDependency() {
        return dependency;
    }

    public void setDependency(Boolean dependency) {
        this.dependency = dependency;
    }

    public List<String> getDependencies() {
        return dependencies;
    }

    public void setDependencies(List<String> dependencies) {
        this.dependencies = dependencies;
    }


    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }


    public List<ModuleConfig> getModules() {
        return modules;
    }

    public void setModules(List<ModuleConfig> modules) {
        this.modules = modules;
    }

//    private Map<String, ModuleConfig> modules;
    private List<FieldConfig> fields;

    public List<FieldConfig> getFields() {
        return fields;
    }

    public void setFields(List<FieldConfig> fields) {
        this.fields = fields;
    }

//    public Map<String, ModuleConfig> getModules() {
//        return modules;
//    }
//
//    public void setModules(Map<String, ModuleConfig> modules) {
//        this.modules = modules;
}







