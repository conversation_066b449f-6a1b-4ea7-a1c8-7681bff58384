package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.umgmt.Role;
import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.ascent.solidus.core.domain.module.AppUser;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleAssignmentRepository extends JpaRepository<RoleAssignment, Long> {
    List<RoleAssignment> findByAppUserAndActive(AppUser appUser, boolean active);
    List<RoleAssignment> findByTenantAndActive(Tenant tenant, boolean active);
    Optional<RoleAssignment> findByAppUserAndRoleAndTenantAndActive(AppUser appUser, Role role, Tenant tenant, boolean active);

    @Query("SELECT ra FROM RoleAssignment ra WHERE ra.appUser.id = :userId AND ra.active = true")
    List<RoleAssignment> findAllActiveRolesByUserId(Long userId);
}