package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AssemblyRepository extends JpaRepository<Assembly, String>, JpaSpecificationExecutor<Assembly> {
  @Query("SELECT e.product FROM Assembly e WHERE e.id = :id")
  Product getProductByAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM Assembly r WHERE r.product.id = :productId")
  List<Assembly> findProductByAssemblyId(@Param("productId") String productId);

  @Query("SELECT e.revision FROM Assembly e WHERE e.id = :id")
  Revision getRevisionByAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM Assembly r WHERE r.revision.id = :revisionId")
  List<Assembly> findRevisionByAssemblyId(@Param("revisionId") String revisionId);

  @Query("SELECT e.parent FROM Assembly e WHERE e.id = :id")
  Assembly getAssemblyByAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM Assembly r WHERE r.parent.id = :assemblyId")
  List<Assembly> findAssemblyByAssemblyId(@Param("assemblyId") String assemblyId);
}
