package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.domain.tenant.Tenant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrganizationRepository extends JpaRepository<Organization, Long> {
    List<Organization> findByTenantAndActive(Tenant tenant, boolean active);
    Optional<Organization> findByIdAndActive(Long id, boolean active);
    Optional<Organization> findByEmailAndActive(String email, boolean active);
}