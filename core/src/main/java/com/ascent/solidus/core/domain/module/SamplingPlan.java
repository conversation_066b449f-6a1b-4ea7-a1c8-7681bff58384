package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.InspectionLevel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.lang.String;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "samplingplan"
)
public class SamplingPlan {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private InspectionLevel inspectionlevel;

  @ManyToOne
  @JoinColumn(
      name = "spotBuyCategory_id"
  )
  private SpotBuyCategory spotBuyCategory;

  @OneToMany
  @JoinTable(
      name = "limits_mapping"
  )
  private List<SamplingPlanLimit> limits;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public InspectionLevel getInspectionLevel() {
    return this.inspectionlevel;
  }

  public void setInspectionLevel(InspectionLevel inspectionlevel) {
    this.inspectionlevel = inspectionlevel;
  }

  public SpotBuyCategory getSpotBuyCategory() {
    return this.spotBuyCategory;
  }

  public void setSpotBuyCategory(SpotBuyCategory spotBuyCategory) {
    this.spotBuyCategory = spotBuyCategory;
  }

  public List<SamplingPlanLimit> getLimits() {
    return this.limits;
  }

  public void setLimits(List<SamplingPlanLimit> limits) {
    this.limits = limits;
  }
}
