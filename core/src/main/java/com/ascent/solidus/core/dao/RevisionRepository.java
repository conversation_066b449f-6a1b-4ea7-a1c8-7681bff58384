package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RevisionRepository extends JpaRepository<Revision, String>, JpaSpecificationExecutor<Revision> {
  @Query("SELECT e.product FROM Revision e WHERE e.id = :id")
  Product getProductByRevisionId(@Param("id") String id);

  @Query("SELECT r FROM Revision r WHERE r.product.id = :productId")
  List<Revision> findProductByRevisionId(@Param("productId") String productId);
}
