package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.ProductFamily;
import com.ascent.solidus.core.domain.module.ProductLabel;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductFamilyRepository extends JpaRepository<ProductFamily, String>, JpaSpecificationExecutor<ProductFamily> {
  @Query("SELECT e.productLabel FROM ProductFamily e WHERE e.id = :id")
  ProductLabel getProductLabelByProductFamilyId(@Param("id") String id);

  @Query("SELECT r FROM ProductFamily r WHERE r.productLabel.id = :productlabelId")
  List<ProductFamily> findProductLabelByProductFamilyId(
      @Param("productlabelId") String productlabelId);
}
