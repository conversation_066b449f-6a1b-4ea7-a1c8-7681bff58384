package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.Double;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "stage"
)
public class Stage {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @Column(
      name = "stageName",
      nullable = false
  )
  private String stageName;

  @Column(
      name = "ordering",
      nullable = true
  )
  private Double ordering;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id",
      referencedColumnName = "id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "stageCategory_id",
      referencedColumnName = "id"
  )
  private StageCategory stageCategory;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public String getStageName() {
    return this.stageName;
  }

  public void setStageName(String stageName) {
    this.stageName = stageName;
  }

  public Double getOrdering() {
    return this.ordering;
  }

  public void setOrdering(Double ordering) {
    this.ordering = ordering;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public StageCategory getStageCategory() {
    return this.stageCategory;
  }

  public void setStageCategory(StageCategory stageCategory) {
    this.stageCategory = stageCategory;
  }
}
