package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.FieldConfig;
import com.squareup.javapoet.*;
import org.springframework.stereotype.Component;

import javax.lang.model.element.Modifier;
import java.io.File;
import java.io.IOException;

@Component
public class DynamicEnumGenerator {
    private static final String ENUM_PACKAGE = "com.ascent.solidus.core.constants";
    private static final String OUTPUT_DIR = "src/main/java";

    public void generateEnumClass(FieldConfig field) throws IOException {
        if (!"enum".equalsIgnoreCase(field.getFieldType())) {
            return;
        }

        String enumName = capitalize(field.getFieldName());
        TypeSpec.Builder enumBuilder = TypeSpec.enumBuilder(enumName)
                .addModifiers(Modifier.PUBLIC);

        // Add simple enum values without anonymous classes
        for (String enumValue : field.getEnumValues()) {
            enumBuilder.addEnumConstant(enumValue);
        }

        JavaFile javaFile = JavaFile.builder(ENUM_PACKAGE, enumBuilder.build())
                .build();
        javaFile.writeTo(new File(OUTPUT_DIR));
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}