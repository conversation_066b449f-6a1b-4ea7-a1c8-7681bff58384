package com.ascent.solidus.core.domain.module;

import jakarta.persistence.*;

import java.util.List;

//@Entity
//@Table(name ="module_config")
public class ModuleConfig {

    private String moduleId;
    private String moduleName;
    private String parentModuleId;

//    @Transient
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_module_id", referencedColumnName = "module_id")
    private ParentModule parentModule;

    private List<FieldConfig> fields;
    private String moduleType;
    private List<String> dependencies;
    private Boolean dependency;
    private List<QueryConfig> queryConfigs;

    // Constructors
    public ModuleConfig() {}

    public ModuleConfig(String moduleId, String moduleName, String parentModuleId,
                        List<FieldConfig> fields, List<QueryConfig> queryConfigs) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
        this.parentModuleId = parentModuleId;
        this.fields = fields;
        this.queryConfigs = queryConfigs;
    }

    // Getters and Setters
    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public ParentModule getParentModule() {
        if (parentModule == null && parentModuleId != null) {
            parentModule = new ParentModule();
            parentModule.setModuleId(parentModuleId);
        }
        return parentModule;
    }

    public void setParentModule(ParentModule parentModule) {
        this.parentModule = parentModule;
        this.parentModuleId = parentModule != null ? parentModule.getModuleId() : null;
    }

    public String getParentModuleId() {
        return parentModuleId != null ? parentModuleId :
                (parentModule != null ? parentModule.getModuleId() : null);
    }

    public void setParentModuleId(String parentModuleId) {
        this.parentModuleId = parentModuleId;
        if (parentModule == null && parentModuleId != null) {
            parentModule = new ParentModule();
            parentModule.setModuleId(parentModuleId);
        }
    }

    public List<FieldConfig> getFields() {
        return fields;
    }

    public void setFields(List<FieldConfig> fields) {
        this.fields = fields;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public List<String> getDependencies() {
        return dependencies;
    }

    public void setDependencies(List<String> dependencies) {
        this.dependencies = dependencies;
    }

    public Boolean getDependency() {
        return dependency;
    }

    public void setDependency(Boolean dependency) {
        this.dependency = dependency;
    }

    public List<QueryConfig> getQueryConfigs() {
        return queryConfigs;
    }

    public void setQueryConfigs(List<QueryConfig> queryConfigs) {
        this.queryConfigs = queryConfigs;
    }

    // Helper methods
    public boolean isIndependent() {
        return "independent".equalsIgnoreCase(moduleType);
    }

    public boolean isDependent() {
        return "dependent".equalsIgnoreCase(moduleType);
    }

    public boolean isParent() {
        return "parent".equalsIgnoreCase(moduleType);
    }

    @Override
    public String toString() {
        return "ModuleConfig{" +
                "moduleId='" + moduleId + '\'' +
                ", moduleName='" + moduleName + '\'' +
                ", parentModuleId='" + parentModuleId + '\'' +
                ", moduleType='" + moduleType + '\'' +
                ", dependency=" + dependency +
                ", fields=" + fields +
                ", queryConfigs=" + queryConfigs +
                '}';
    }
}