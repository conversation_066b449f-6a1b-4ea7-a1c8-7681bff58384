package com.ascent.solidus.core.dao;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class DynamicEntityDaoImpl implements DynamicEntityDao {

    private final EntityManager entityManager;
    private final DynamicEntityRegistry registry;

    @Autowired
    public DynamicEntityDaoImpl(EntityManager entityManager,
                                DynamicEntityRegistry registry) {
        this.entityManager = entityManager;
        this.registry = registry;
    }

    @Override
    public Object create(String entityType, Map<String, Object> fieldValues) {
        try {
            Class<?> entityClass = registry.getEntityClass(entityType);
            Object entity = entityClass.getDeclaredConstructor().newInstance();

            BeanWrapper wrapper = new BeanWrapperImpl(entity);
            for (Map.Entry<String, Object> entry : fieldValues.entrySet()) {
                wrapper.setPropertyValue(entry.getKey(), entry.getValue());
            }

            entityManager.persist(entity);
            return entity;
        } catch (Exception e) {
            throw new DataAccessException("Failed to create entity", e) {};
        }
    }

    @Override
    public Object findById(String entityType, String id) {
        Class<?> entityClass = registry.getEntityClass(entityType);
        return entityManager.find(entityClass, id);
    }

    @Override
    public List<Object> findAll(String entityType) {
        Class<?> entityClass = registry.getEntityClass(entityType);
        CriteriaQuery<Object> query = (CriteriaQuery<Object>) entityManager.getCriteriaBuilder()
                .createQuery(entityClass);
        query.select(query.from(entityClass));
        return entityManager.createQuery(query).getResultList();
    }

    @Override
    public Object update(String entityType, String id, Map<String, Object> updates) {
        Object entity = findById(entityType, id);
        if (entity != null) {
            BeanWrapper wrapper = new BeanWrapperImpl(entity);
            for (Map.Entry<String, Object> entry : updates.entrySet()) {
                wrapper.setPropertyValue(entry.getKey(), entry.getValue());
            }
            return entityManager.merge(entity);
        }
        return null;
    }

    @Override
    public void delete(String entityType, String id) {
        Object entity = findById(entityType, id);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    public List<Object> findByParentModule(String entityType, String parentModuleId) throws Exception{
        Class<?> entityClass = registry.getEntityClass(entityType);

        // Check if the entity has a parentModuleId field
//        try {
            entityClass.getDeclaredField("parentModuleId");
//        } catch (NoSuchFieldException e) {
//            throw new EntityOperationException(
//                    "Entity type " + entityType + " doesn't support parent-child relationships",
//                    "INVALID_ENTITY_TYPE"
//            );
//        }

        // Create a JPA query using the entity class
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Object> query = (CriteriaQuery<Object>) cb.createQuery(entityClass);
        Root<?> root = query.from(entityClass);

        // Add where clause for parentModuleId
        query.where(cb.equal(root.get("parentModuleId"), parentModuleId));

        return entityManager.createQuery(query).getResultList();
    }
}
