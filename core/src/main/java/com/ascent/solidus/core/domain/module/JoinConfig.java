package com.ascent.solidus.core.domain.module;

public class JoinConfig {
    private String path;
    private String type;  // "INNER_JOIN", "LEFT_OUTER_JOIN", "RIGHT_JOIN", etc.

    // Constructors
    public JoinConfig() {}

    public JoinConfig(String path, String type) {
        this.path = path;
        this.type = type;
    }

    // Getters and Setters
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "JoinConfig{" +
                "path='" + path + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
