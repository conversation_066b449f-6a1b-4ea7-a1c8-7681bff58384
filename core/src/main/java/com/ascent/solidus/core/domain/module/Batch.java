package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "batch"
)
public class Batch {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "workOrder_id"
  )
  private WorkOrder workOrder;

  @ManyToOne
  @JoinColumn(
      name = "client_id"
  )
  private Client client;

  @ManyToOne
  @JoinColumn(
      name = "revision_id"
  )
  private Revision revision;

  @ManyToOne
  @JoinColumn(
      name = "createdBy_id"
  )
  private AppUser createdBy;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public WorkOrder getWorkOrder() {
    return this.workOrder;
  }

  public void setWorkOrder(WorkOrder workOrder) {
    this.workOrder = workOrder;
  }

  public Client getClient() {
    return this.client;
  }

  public void setClient(Client client) {
    this.client = client;
  }

  public Revision getRevision() {
    return this.revision;
  }

  public void setRevision(Revision revision) {
    this.revision = revision;
  }

  public AppUser getCreatedBy() {
    return this.createdBy;
  }

  public void setCreatedBy(AppUser createdBy) {
    this.createdBy = createdBy;
  }
}
