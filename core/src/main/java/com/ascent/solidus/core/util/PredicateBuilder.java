package com.ascent.solidus.core.util;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PredicateBuilder {
    public static <T> Specification<T> buildSpecification(Map<String, Object> filters) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            filters.forEach((field, value) -> {
                if (value != null) {
                    if (value instanceof String) {
                        predicates.add(cb.like(root.get(field), "%" + value + "%"));
                    } else {
                        predicates.add(cb.equal(root.get(field), value));
                    }
                }
            });
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}