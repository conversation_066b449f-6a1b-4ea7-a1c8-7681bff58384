package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantRepository extends JpaRepository<Tenant, Long> {
    Optional<Tenant> findByIdAndActive(Long id, boolean active);
    List<Tenant> findByTenantTypeAndActive(TenantType tenantType, boolean active);
}