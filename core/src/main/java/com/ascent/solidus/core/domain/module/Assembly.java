package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.Double;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assembly"
)
public class Assembly {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @Column(
      name = "name",
      nullable = false
  )
  private String name;

  @Column(
      name = "revisionNumber",
      nullable = true
  )
  private String revisionNumber;

  @Column(
      name = "drawingNumber",
      nullable = true
  )
  private String drawingNumber;

  @Column(
      name = "serialNumberLength",
      nullable = true
  )
  private Double serialNumberLength;

  @ManyToOne
  @JoinColumn(
      name = "product_id",
      referencedColumnName = "id"
  )
  private Product product;

  @ManyToOne
  @JoinColumn(
      name = "revision_id",
      referencedColumnName = "id"
  )
  private Revision revision;

  @ManyToOne
  @JoinColumn(
      name = "parent_id",
      referencedColumnName = "id"
  )
  private Assembly parent;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getRevisionNumber() {
    return this.revisionNumber;
  }

  public void setRevisionNumber(String revisionNumber) {
    this.revisionNumber = revisionNumber;
  }

  public String getDrawingNumber() {
    return this.drawingNumber;
  }

  public void setDrawingNumber(String drawingNumber) {
    this.drawingNumber = drawingNumber;
  }

  public Double getSerialNumberLength() {
    return this.serialNumberLength;
  }

  public void setSerialNumberLength(Double serialNumberLength) {
    this.serialNumberLength = serialNumberLength;
  }

  public Product getProduct() {
    return this.product;
  }

  public void setProduct(Product product) {
    this.product = product;
  }

  public Revision getRevision() {
    return this.revision;
  }

  public void setRevision(Revision revision) {
    this.revision = revision;
  }

  public Assembly getParent() {
    return this.parent;
  }

  public void setParent(Assembly parent) {
    this.parent = parent;
  }
}
