package com.ascent.solidus.core.domain.module;

import java.util.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Validation;

public class FieldConfig {
    private String fieldName;
    private String fieldType;
    private boolean required;

    private List<String> enumValues;

    public List<String> getEnumValues() {
        return enumValues;
    }

    public void setEnumValues(List<String> enumValues) {
        this.enumValues = enumValues;
    }

    public String getReferenceModule() {
        return referenceModule;
    }

    public void setReferenceModule(String referenceModule) {
        this.referenceModule = referenceModule;
    }

    private String referenceModule;

    private Boolean choice;
    @JsonProperty("valid")
    private Boolean valid = false;

    @JsonProperty("transient")
    private Boolean isTransient = false;

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public Boolean getTransient() {
        return isTransient;
    }

    public void setTransient(Boolean istransient) {
        isTransient = istransient;
    }


    public Boolean getChoice() {
        return choice;
    }

    public void setChoice(Boolean choice) {
        this.choice = choice;
    }


    // Constructors
    public FieldConfig() {}

    public FieldConfig(String fieldName, String fieldType, boolean required) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.required = required;
    }

//    public BasicDetails getBasicDetails() {
//        return basicDetails;
//    }
//
//    public void setBasicDetails(BasicDetails basicDetails) {
//        this.basicDetails = basicDetails;
//    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

//    public Validation getValidation() {
//        return validation;
//    }
//
//    public void setValidation(Validation validation) {
//        this.validation = validation;
//    }
//
//    public String getDataSource() {
//        return dataSource;
//    }
//
//    public void setDataSource(String dataSource) {
//        this.dataSource = dataSource;
//    }
//
//    public String getFieldSource() {
//        return fieldSource;
//    }
//
//    public void setFieldSource(String fieldSource) {
//        this.fieldSource = fieldSource;
//    }
//
//    public String getFieldId() {
//        return fieldId;
//    }
//
//    public void setFieldId(String fieldId) {
//        this.fieldId = fieldId;
//    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

//    private BasicDetails basicDetails;
//    private Validation validation;
//    private String dataSource;
//    private String fieldSource;
//    private String fieldId;

}
