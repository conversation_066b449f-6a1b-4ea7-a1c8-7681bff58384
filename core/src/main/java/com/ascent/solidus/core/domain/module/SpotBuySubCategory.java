package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.SpotBuyType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "spotbuysubcategory"
)
public class SpotBuySubCategory {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private SpotBuyType spotbuytype;

  @ManyToOne
  @JoinColumn(
      name = "material_id"
  )
  private Material material;

  @ManyToOne
  @JoinColumn(
      name = "materialGroup_id"
  )
  private MaterialGroup materialGroup;

  @ManyToOne
  @JoinColumn(
      name = "spotBuyCategory_id"
  )
  private SpotBuyCategory spotBuyCategory;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public SpotBuyType getSpotBuyType() {
    return this.spotbuytype;
  }

  public void setSpotBuyType(SpotBuyType spotbuytype) {
    this.spotbuytype = spotbuytype;
  }

  public Material getMaterial() {
    return this.material;
  }

  public void setMaterial(Material material) {
    this.material = material;
  }

  public MaterialGroup getMaterialGroup() {
    return this.materialGroup;
  }

  public void setMaterialGroup(MaterialGroup materialGroup) {
    this.materialGroup = materialGroup;
  }

  public SpotBuyCategory getSpotBuyCategory() {
    return this.spotBuyCategory;
  }

  public void setSpotBuyCategory(SpotBuyCategory spotBuyCategory) {
    this.spotBuyCategory = spotBuyCategory;
  }
}
