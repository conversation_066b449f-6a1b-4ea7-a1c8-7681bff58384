package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.LimitCategory;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "dashboardlimitvalue"
)
public class DashboardLimitValue {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private LimitCategory limitcategory;

  @ManyToOne
  @JoinColumn(
      name = "factory_id"
  )
  private Factory factory;

  @ManyToOne
  @JoinColumn(
      name = "dashboardLimit_id"
  )
  private DashboardLimit dashboardLimit;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public LimitCategory getLimitCategory() {
    return this.limitcategory;
  }

  public void setLimitCategory(LimitCategory limitcategory) {
    this.limitcategory = limitcategory;
  }

  public Factory getFactory() {
    return this.factory;
  }

  public void setFactory(Factory factory) {
    this.factory = factory;
  }

  public DashboardLimit getDashboardLimit() {
    return this.dashboardLimit;
  }

  public void setDashboardLimit(DashboardLimit dashboardLimit) {
    this.dashboardLimit = dashboardLimit;
  }
}
