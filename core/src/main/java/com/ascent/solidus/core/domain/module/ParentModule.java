package com.ascent.solidus.core.domain.module;

import jakarta.persistence.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "parent_modules")
public class ParentModule {
    
    @Id
    private String moduleId;
    
    @Column(name = "module_name")
    private String moduleName;
    
//    @Column(name = "module_icon")
//    private String moduleIcon;
//
//    @Column(name = "is_component")
//    private boolean isComponent;

//    public boolean isStandalone() {
//        return isStandalone;
//    }
//
//    public void setStandalone(boolean standalone) {
//        isStandalone = standalone;
//    }
//
//    @Column(name = "is_standalone")
//    private boolean isStandalone;

//    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "parent_modules")
    @Transient
//    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
//    @JoinColumn(name = "parent_module_id")
    private Set<ModuleConfig> modules;

    private String moduleType;

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

//    @OneToMany(mappedBy = "ParentModule", cascade = CascadeType.ALL, orphanRemoval = true)
//    private Set<Object> subModules = new HashSet<>();
//
//    public Set<Object> getSubModules() {
//        return subModules;
//    }
//
//    public void setSubModules(Set<Object> subModules) {
//        this.subModules = subModules;
//    }

    // Constructors
    public ParentModule() {}
    
    public ParentModule(String moduleId, String moduleName) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
//        this.moduleIcon = moduleIcon;
//        this.isComponent = isComponent;
    }
    
    // Getters and Setters
    public String getModuleId() {
        return moduleId;
    }
    
    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }
    
    public String getModuleName() {
        return moduleName;
    }
    
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
    
//    public String getModuleIcon() {
//        return moduleIcon;
//    }
//
//    public void setModuleIcon(String moduleIcon) {
//        this.moduleIcon = moduleIcon;
//    }
//
//    public boolean isComponent() {
//        return isComponent;
//    }
//
//    public void setIsComponent(boolean component) {
//        isComponent = component;
//    }

    public Set<ModuleConfig> getModules() {
        return modules;
    }

    public void setModules(Set<ModuleConfig> modules) {
        this.modules = modules;
    }
}

