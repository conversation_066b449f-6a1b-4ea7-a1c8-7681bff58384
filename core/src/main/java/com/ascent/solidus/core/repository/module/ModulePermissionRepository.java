//package com.ascent.solidus.core.repository.module;
//
//import com.ascent.solidus.core.constants.RoleName;
//import com.ascent.solidus.core.domain.module.Module;
//import com.ascent.solidus.core.domain.module.ModulePermission;
//import com.ascent.solidus.core.domain.umgmt.Role;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//import java.util.Optional;
//
//@Repository
//public interface ModulePermissionRepository extends JpaRepository<ModulePermission, Long> {
//    List<ModulePermission> findByRoleAndActive(Role role, boolean active);
//
//    Optional<ModulePermission> findByRoleAndModuleAndActive(Role role, Module module, boolean active);
//
//    @Query("SELECT mp.module.id FROM ModulePermission mp WHERE mp.role.name = :roleName AND mp.active = true")
//    List<Long> findModuleIdsByRoleName(RoleName roleName);
//
//    void deleteByRoleAndModule(Role role, Module module);
//
//    void deleteByRole(Role role);
//}