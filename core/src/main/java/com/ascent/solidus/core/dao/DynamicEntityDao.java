package com.ascent.solidus.core.dao;

import java.util.List;
import java.util.Map;

public interface DynamicEntityDao {

    Object create(String entityType, Map<String, Object> fieldValues);
    Object findById(String entityType, String id);
    List<Object> findAll(String entityType);
    Object update(String entityType, String id, Map<String, Object> updates);
    void delete(String entityType, String id);
    List<Object> findByParentModule(String entityType, String parentModuleId) throws Exception;
}
