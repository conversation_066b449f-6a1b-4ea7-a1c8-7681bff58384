package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.*;
import com.squareup.javapoet.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.lang.model.element.Modifier;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class DynamicRepositoryGenerator {
    private static final String REPO_PACKAGE = "com.ascent.solidus.core.dao";
    private static final String OUTPUT_DIR = "src/main/java";
    private static final String ENTITY_PACKAGE = "com.ascent.solidus.core.domain.module";

    public JavaFile generateRepositoryInterface(Class<?> entityClass, ModuleConfig moduleConfig) throws IOException {
        String repositoryName = entityClass.getSimpleName() + "Repository";
        String entityClassName = entityClass.getSimpleName();

        TypeSpec.Builder repositoryBuilder = TypeSpec.interfaceBuilder(repositoryName)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(Repository.class)
                .addSuperinterface(ParameterizedTypeName.get(
                        ClassName.get(JpaRepository.class),
                        ClassName.get(entityClass),
                        ClassName.get(String.class)))
                .addSuperinterface(ParameterizedTypeName.get(
                        ClassName.get("org.springframework.data.jpa.repository", "JpaSpecificationExecutor"),
                        ClassName.get(entityClass)));

        // Handle reference fields (forward relationships)
        if (moduleConfig.getFields() != null) {
            List<FieldConfig> referenceFields = moduleConfig.getFields().stream()
                    .filter(f -> "reference".equalsIgnoreCase(f.getFieldType()))
                    .filter(f -> !f.getTransient() && f.getValid())
                    .collect(Collectors.toList());

            for (FieldConfig refField : referenceFields) {
                String refEntityName = getValidEntityName(refField.getReferenceModule());
                String fieldName = refField.getFieldName();

                // Method to get referenced entity
                repositoryBuilder.addMethod(generateGetReferencedEntityMethod(
                        entityClassName,
                        refEntityName,
                        fieldName));

                repositoryBuilder.addMethod(generateFindReferencingEntitiesMethod(
                        entityClassName,
                        refEntityName, fieldName));
            }
        }

        // Handle query configurations
        if (moduleConfig.getQueryConfigs() != null && !moduleConfig.getQueryConfigs().isEmpty()) {
            for (QueryConfig queryConfig : moduleConfig.getQueryConfigs()) {
                repositoryBuilder.addMethod(generateQueryMethod(entityClassName, queryConfig));
            }
        }

        JavaFile javaFile = JavaFile.builder(REPO_PACKAGE, repositoryBuilder.build())
                .build();
        javaFile.writeTo(new File(OUTPUT_DIR));
        return javaFile;
    }

    private MethodSpec generateQueryMethod(String entityClassName, QueryConfig queryConfig) {
        MethodSpec.Builder methodBuilder = MethodSpec.methodBuilder(queryConfig.getName())
                .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
                .addAnnotation(AnnotationSpec.builder(Query.class)
                        .addMember("value", "$S", buildQueryString(entityClassName, queryConfig))
                        .build());

        // Handle return type
        TypeName returnType = determineReturnType(queryConfig);
        methodBuilder.returns(returnType);

        // Add parameters for filters with values
        if (queryConfig.getFilters() != null) {
            for (FilterConfig filter : queryConfig.getFilters()) {
                if (filter.getValue() != null && !filter.getValue().toString().startsWith(":")) {
                    String paramName = filter.getField().replace(".", "_");
                    methodBuilder.addParameter(ParameterSpec.builder(Object.class, paramName)
                            .addAnnotation(AnnotationSpec.builder(Param.class)
                                    .addMember("value", "$S", paramName)
                                    .build())
                            .build());
                }
            }
        }

        // Add parameter for having clause
        if (queryConfig.getHaving() != null && !queryConfig.getHaving().isEmpty()) {
            for (HavingConfig having : queryConfig.getHaving()) {
                String paramName = having.getField().replace(".", "_");
                methodBuilder.addParameter(ParameterSpec.builder(Object.class, paramName)
                        .addAnnotation(AnnotationSpec.builder(Param.class)
                                .addMember("value", "$S", paramName)
                                .build())
                        .build());
            }
        }

        return methodBuilder.build();
    }

    private TypeName determineReturnType(QueryConfig queryConfig) {
        String returnType = queryConfig.getReturnType();
        String returnClass = queryConfig.getReturnClass();

        if (returnType == null || returnType.isEmpty()) {
            // Default to single entity if no return type specified
            return getTypeName(returnClass != null ? returnClass : queryConfig.getEntity());
        }

        switch (returnType.toLowerCase()) {
            case "list":
                return ParameterizedTypeName.get(
                        ClassName.get(List.class),
                        getTypeName(returnClass != null ? returnClass : queryConfig.getEntity()));
            case "set":
                return ParameterizedTypeName.get(
                        ClassName.get(Set.class),
                        getTypeName(returnClass != null ? returnClass : queryConfig.getEntity()));
            case "map":
                return ParameterizedTypeName.get(
                        ClassName.get(Map.class),
                        ClassName.get(String.class),
                        getTypeName(returnClass != null ? returnClass : queryConfig.getEntity()));
            default:
                return getTypeName(returnType);
        }
    }

    private TypeName getTypeName(String className) {
        // First check if it's a Java built-in type
        switch (className.toLowerCase()) {
            case "string":
                return ClassName.get(String.class);
            case "integer":
            case "int":
                return ClassName.get(Integer.class);
            case "long":
                return ClassName.get(Long.class);
            case "boolean":
                return ClassName.get(Boolean.class);
            case "double":
                return ClassName.get(Double.class);
            case "float":
                return ClassName.get(Float.class);
            default:
                return ClassName.get(ENTITY_PACKAGE, className);
        }
    }

    private String buildQueryString(String entityClassName, QueryConfig queryConfig) {
        StringBuilder queryBuilder = new StringBuilder("SELECT ");

        // Handle what to select - support both aggregations and specific fields
        if (queryConfig.getAggregations() != null && !queryConfig.getAggregations().isEmpty()) {
            for (AggregationConfig agg : queryConfig.getAggregations()) {
                queryBuilder.append(agg.getFunction())
                        .append("(")
                        .append(agg.getField().contains(".") ? agg.getField() : "e." + agg.getField())
                        .append(") as ")
                        .append(agg.getAlias())
                        .append(", ");
            }
            queryBuilder.setLength(queryBuilder.length() - 2); // Remove trailing comma
        } else if (queryConfig.getSelectFields() != null && !queryConfig.getSelectFields().isEmpty()) {
            // Handle specific field selection
            for (String field : queryConfig.getSelectFields()) {
                queryBuilder.append(field.contains(".") ? field : "e." + field)
                        .append(", ");
            }
            queryBuilder.setLength(queryBuilder.length() - 2);
        } else {
            queryBuilder.append("e"); // Default to selecting the entity
        }

        queryBuilder.append(" FROM ").append(entityClassName).append(" e");

        // Handle joins with proper JPQL syntax
        if (queryConfig.getJoins() != null) {
            for (JoinConfig join : queryConfig.getJoins()) {
                String joinType = join.getType().replace("_", " "); // Convert INNER_JOIN to INNER JOIN
                queryBuilder.append(" ").append(joinType).append(" ");
                queryBuilder.append(join.getPath().contains(".") ? join.getPath() : "e." + join.getPath());
            }
        }

        // Handle filters with proper operator translation
        if (queryConfig.getFilters() != null && !queryConfig.getFilters().isEmpty()) {
            queryBuilder.append(" WHERE ");
            for (FilterConfig filter : queryConfig.getFilters()) {
                String fieldPath = filter.getField().contains(".") ? filter.getField() : "e." + filter.getField();
                queryBuilder.append(fieldPath)
                        .append(" ")
                        .append(translateOperator(filter.getOperator())) // Convert eq to =, etc.
                        .append(" ");

                if (filter.getValue().toString().startsWith(":")) {
                    queryBuilder.append(filter.getValue());
                } else {
                    queryBuilder.append(":").append(filter.getField().replace(".", "_"));
                }
                queryBuilder.append(" AND ");
            }
            queryBuilder.setLength(queryBuilder.length() - 5); // Remove trailing AND
        }

        // Handle group by with proper field qualification
        if (queryConfig.getGroupBy() != null && !queryConfig.getGroupBy().isEmpty()) {
            queryBuilder.append(" GROUP BY ");
            for (String groupBy : queryConfig.getGroupBy()) {
                queryBuilder.append(groupBy.contains(".") ? groupBy : "e." + groupBy)
                        .append(", ");
            }
            queryBuilder.setLength(queryBuilder.length() - 2);
        }

        // Handle having clause
        if (queryConfig.getHaving() != null && !queryConfig.getHaving().isEmpty()) {
            queryBuilder.append(" HAVING ");
            for (HavingConfig having : queryConfig.getHaving()) {
                queryBuilder.append(having.getField())
                        .append(" ")
                        .append(translateOperator(having.getOperator()))
                        .append(" :").append(having.getField().replace(".", "_"))
                        .append(" AND ");
            }
            queryBuilder.setLength(queryBuilder.length() - 5);
        }

        // Handle sort with proper field qualification
        if (queryConfig.getSort() != null && !queryConfig.getSort().isEmpty()) {
            queryBuilder.append(" ORDER BY ");
            for (SortConfig sort : queryConfig.getSort()) {
                queryBuilder.append(sort.getField().contains(".") ? sort.getField() : "e." + sort.getField())
                        .append(" ")
                        .append(sort.getDirection())
                        .append(", ");
            }
            queryBuilder.setLength(queryBuilder.length() - 2);
        }

        return queryBuilder.toString();
    }


    // Helper method to translate operators
    private String translateOperator(String operator) {
        switch (operator.toLowerCase()) {
            case "eq": return "=";
            case "ne": return "<>";
            case "gt": return ">";
            case "gte": return ">=";
            case "lt": return "<";
            case "lte": return "<=";
            case "like": return "LIKE";
            default: return operator;
        }
    }

    private MethodSpec generateGetReferencedEntityMethod(String entityClassName,
                                                         String refEntityName,
                                                         String fieldName) {
        return MethodSpec.methodBuilder("get" + capitalize(refEntityName) + "By" + entityClassName + "Id")
                .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
                .addAnnotation(AnnotationSpec.builder(Query.class)
                        .addMember("value", "$S",
                                "SELECT e." + fieldName + " FROM " + entityClassName + " e WHERE e.id = :id")
                        .build())
                .returns(ClassName.get(ENTITY_PACKAGE, refEntityName))
                .addParameter(ParameterSpec.builder(String.class, "id")
                        .addAnnotation(AnnotationSpec.builder(Param.class)
                                .addMember("value", "$S", "id")
                                .build())
                        .build())
                .build();
    }

    private MethodSpec generateFindReferencingEntitiesMethod(String entityClassName,
                                                             String refEntityName, String fieldName) {
        String methodName = "find" + capitalize(refEntityName) + "By" + entityClassName + "Id";
        String paramName = refEntityName.toLowerCase() + "Id";

        return MethodSpec.methodBuilder(methodName)
                .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
                .addAnnotation(AnnotationSpec.builder(Query.class)
                        .addMember("value", "$S",
                                "SELECT r FROM " + entityClassName + " r WHERE r." +
                                        fieldName + ".id = :" + paramName)
                        .build())
                .returns(ParameterizedTypeName.get(
                        ClassName.get(List.class),
                        ClassName.get(ENTITY_PACKAGE, entityClassName)))
                .addParameter(ParameterSpec.builder(String.class, paramName)
                        .addAnnotation(AnnotationSpec.builder(Param.class)
                                .addMember("value", "$S", paramName)
                                .build())
                        .build())
                .build();
    }

    private String getValidEntityName(String referenceModule) {
        if (referenceModule.matches("^\\d.*")) {
            return "Module" + referenceModule;
        }
        return referenceModule;
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}