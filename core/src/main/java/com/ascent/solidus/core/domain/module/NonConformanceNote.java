package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.NonConformanceStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "nonconformancenote"
)
public class NonConformanceNote {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "performer_id"
  )
  private AppUser performer;

  @ManyToOne
  @JoinColumn(
      name = "nonConformance_id"
  )
  private NonConformance nonConformance;

  private NonConformanceStatus nonconformancestatus;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public AppUser getPerformer() {
    return this.performer;
  }

  public void setPerformer(AppUser performer) {
    this.performer = performer;
  }

  public NonConformance getNonConformance() {
    return this.nonConformance;
  }

  public void setNonConformance(NonConformance nonConformance) {
    this.nonConformance = nonConformance;
  }

  @Enumerated(EnumType.STRING)
  public NonConformanceStatus getNonConformanceStatus() {
    return this.nonconformancestatus;
  }

  public void setNonConformanceStatus(NonConformanceStatus nonconformancestatus) {
    this.nonconformancestatus = nonconformancestatus;
  }
}
