package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.SpotBuyStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "categoryinspection"
)
public class CategoryInspection {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "category_id"
  )
  private SpotBuySubCategory category;

  @ManyToOne
  @JoinColumn(
      name = "inspector_id"
  )
  private AppUser inspector;

  private SpotBuyStatus spotbuystatus;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public SpotBuySubCategory getCategory() {
    return this.category;
  }

  public void setCategory(SpotBuySubCategory category) {
    this.category = category;
  }

  public AppUser getInspector() {
    return this.inspector;
  }

  public void setInspector(AppUser inspector) {
    this.inspector = inspector;
  }

  @Enumerated(EnumType.STRING)
  public SpotBuyStatus getSpotBuyStatus() {
    return this.spotbuystatus;
  }

  public void setSpotBuyStatus(SpotBuyStatus spotbuystatus) {
    this.spotbuystatus = spotbuystatus;
  }
}
