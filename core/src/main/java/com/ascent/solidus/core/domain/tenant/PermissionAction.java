package com.ascent.solidus.core.domain.tenant;

/**
 * Enum representing different permission actions that can be performed on features and modules
 */
public enum PermissionAction {
    /**
     * Can view/read data
     */
    READ,
    
    /**
     * Can create/update data
     */
    WRITE,
    
    /**
     * Can delete data
     */
    DELETE,
    
    /**
     * Can execute operations/actions
     */
    EXECUTE,
    
    /**
     * Full administrative access
     */
    ADMIN
}
