package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "assignassemblyinspector"
)
public class AssignAssemblyInspector {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "inspector_id"
  )
  private AppUser inspector;

  @ManyToOne
  @JoinColumn(
      name = "assignAssembly_id"
  )
  private AssignAssembly assignAssembly;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public AppUser getInspector() {
    return this.inspector;
  }

  public void setInspector(AppUser inspector) {
    this.inspector = inspector;
  }

  public AssignAssembly getAssignAssembly() {
    return this.assignAssembly;
  }

  public void setAssignAssembly(AssignAssembly assignAssembly) {
    this.assignAssembly = assignAssembly;
  }
}
