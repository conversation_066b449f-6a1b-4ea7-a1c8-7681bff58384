package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.SpotBuyDefectType;
import com.ascent.solidus.core.constants.SpotBuyType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "spotbuydefect"
)
public class SpotBuyDefect {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private SpotBuyDefectType spotbuydefecttype;

  private SpotBuyType spotbuytype;

  @ManyToOne
  @JoinColumn(
      name = "spotBuyCategory_id"
  )
  private SpotBuyCategory spotBuyCategory;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public SpotBuyDefectType getSpotBuyDefectType() {
    return this.spotbuydefecttype;
  }

  public void setSpotBuyDefectType(SpotBuyDefectType spotbuydefecttype) {
    this.spotbuydefecttype = spotbuydefecttype;
  }

  @Enumerated(EnumType.STRING)
  public SpotBuyType getSpotBuyType() {
    return this.spotbuytype;
  }

  public void setSpotBuyType(SpotBuyType spotbuytype) {
    this.spotbuytype = spotbuytype;
  }

  public SpotBuyCategory getSpotBuyCategory() {
    return this.spotBuyCategory;
  }

  public void setSpotBuyCategory(SpotBuyCategory spotBuyCategory) {
    this.spotBuyCategory = spotBuyCategory;
  }
}
