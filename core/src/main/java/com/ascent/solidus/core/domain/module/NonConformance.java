package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.NonConformanceStatus;
import com.ascent.solidus.core.constants.NonConformanceType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.lang.String;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "nonconformance"
)
public class NonConformance {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private NonConformanceStatus nonconformancestatus;

  private NonConformanceType nonconformancetype;

  @ManyToOne
  @JoinColumn(
      name = "raisedBy_id"
  )
  private AppUser raisedBy;

  @ManyToOne
  @JoinColumn(
      name = "closedBy_id"
  )
  private AppUser closedBy;

  @ManyToOne
  @JoinColumn(
      name = "ncStage_id"
  )
  private NcStage ncStage;

  @ManyToOne
  @JoinColumn(
      name = "defectGroup_id"
  )
  private DefectGroup defectGroup;

  @OneToMany
  @JoinTable(
      name = "shift_mapping"
  )
  private List<Shift> shift;

  @OneToMany
  @JoinTable(
      name = "responsiblePerson_mapping"
  )
  private List<AppUser> responsiblePerson;

  @OneToMany
  @JoinTable(
      name = "department_mapping"
  )
  private List<Department> department;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public NonConformanceStatus getNonConformanceStatus() {
    return this.nonconformancestatus;
  }

  public void setNonConformanceStatus(NonConformanceStatus nonconformancestatus) {
    this.nonconformancestatus = nonconformancestatus;
  }

  @Enumerated(EnumType.STRING)
  public NonConformanceType getNonConformanceType() {
    return this.nonconformancetype;
  }

  public void setNonConformanceType(NonConformanceType nonconformancetype) {
    this.nonconformancetype = nonconformancetype;
  }

  public AppUser getRaisedBy() {
    return this.raisedBy;
  }

  public void setRaisedBy(AppUser raisedBy) {
    this.raisedBy = raisedBy;
  }

  public AppUser getClosedBy() {
    return this.closedBy;
  }

  public void setClosedBy(AppUser closedBy) {
    this.closedBy = closedBy;
  }

  public NcStage getNcStage() {
    return this.ncStage;
  }

  public void setNcStage(NcStage ncStage) {
    this.ncStage = ncStage;
  }

  public DefectGroup getDefectGroup() {
    return this.defectGroup;
  }

  public void setDefectGroup(DefectGroup defectGroup) {
    this.defectGroup = defectGroup;
  }

  public List<Shift> getShift() {
    return this.shift;
  }

  public void setShift(List<Shift> shift) {
    this.shift = shift;
  }

  public List<AppUser> getResponsiblePerson() {
    return this.responsiblePerson;
  }

  public void setResponsiblePerson(List<AppUser> responsiblePerson) {
    this.responsiblePerson = responsiblePerson;
  }

  public List<Department> getDepartment() {
    return this.department;
  }

  public void setDepartment(List<Department> department) {
    this.department = department;
  }
}
