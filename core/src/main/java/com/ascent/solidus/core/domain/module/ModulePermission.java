//package com.ascent.solidus.core.domain.module;
//
//import com.ascent.solidus.core.domain.AbstractEntity;
//import com.ascent.solidus.core.domain.umgmt.Role;
//import jakarta.persistence.*;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
////@Entity
////@Table(name = "module_permissions", uniqueConstraints = {
////    @UniqueConstraint(columnNames = {"role_id", "module_id"})
////})
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class ModulePermission extends AbstractEntity {
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "role_id", nullable = false)
//    private Role role;
//
//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "module_id", nullable = false)
//    private Module module;
//
//    @Column(nullable = false)
//    private boolean active = true;
//}