package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.ProductFamily;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductRepository extends JpaRepository<Product, String>, JpaSpecificationExecutor<Product> {
  @Query("SELECT e.productFamily FROM Product e WHERE e.id = :id")
  ProductFamily getProductFamilyByProductId(@Param("id") String id);

  @Query("SELECT r FROM Product r WHERE r.productFamily.id = :productfamilyId")
  List<Product> findProductFamilyByProductId(@Param("productfamilyId") String productfamilyId);
}
