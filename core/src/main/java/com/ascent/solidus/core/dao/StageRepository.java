package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Stage;
import com.ascent.solidus.core.domain.module.StageCategory;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StageRepository extends JpaRepository<Stage, String>, JpaSpecificationExecutor<Stage> {
  @Query("SELECT e.assembly FROM Stage e WHERE e.id = :id")
  Assembly getAssemblyByStageId(@Param("id") String id);

  @Query("SELECT r FROM Stage r WHERE r.assembly.id = :assemblyId")
  List<Stage> findAssemblyByStageId(@Param("assemblyId") String assemblyId);

  @Query("SELECT e.stageCategory FROM Stage e WHERE e.id = :id")
  StageCategory getStageCategoryByStageId(@Param("id") String id);

  @Query("SELECT r FROM Stage r WHERE r.stageCategory.id = :stagecategoryId")
  List<Stage> findStageCategoryByStageId(@Param("stagecategoryId") String stagecategoryId);
}
