package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.FactoryType;
import com.ascent.solidus.core.domain.AbstractEntity;
import com.ascent.solidus.core.domain.umgmt.City;
import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.domain.umgmt.State;
import jakarta.persistence.*;

@Entity
@Table(name = "factory")

public class Factory extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "Id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    private String contactPersonName;

    private String email;

    private String mobile;

    private String otherContactNumber;

    private String addressLine1;

    private String addressLine2;

    private String area;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "City_Id", referencedColumnName = "Id")
    private City city;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "State_Id", referencedColumnName = "Id")
    private State state;

    @ManyToOne(fetch = FetchType.EAGER, optional = true)
    @JoinColumn(name = "Country_Id", referencedColumnName = "Id")
    private Country country;

    private String zipCode;

    private FactoryType type;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOtherContactNumber() {
        return otherContactNumber;
    }

    public void setOtherContactNumber(String otherContactNumber) {
        this.otherContactNumber = otherContactNumber;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public FactoryType getType() {
        return type;
    }

    public void setType(FactoryType type) {
        this.type = type;
    }
}