package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.InspectionActionCriteria;
import com.ascent.solidus.core.constants.InspectionResult;
import com.ascent.solidus.core.constants.InspectionStatus;
import com.ascent.solidus.core.constants.InspectionType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "inspection"
)
public class Inspection {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "factory_id"
  )
  private Factory factory;

  @ManyToOne
  @JoinColumn(
      name = "product_id"
  )
  private Product product;

  @ManyToOne
  @JoinColumn(
      name = "batch_id"
  )
  private Batch batch;

  private InspectionStatus inspectionstatus;

  private InspectionResult inspectionresult;

  private InspectionType inspectiontype;

  private InspectionActionCriteria inspectionactioncriteria;

  @ManyToOne
  @JoinColumn(
      name = "assignee_id"
  )
  private AppUser assignee;

  @ManyToOne
  @JoinColumn(
      name = "inspector_id"
  )
  private AppUser inspector;

  @ManyToOne
  @JoinColumn(
      name = "assignAssembly_id"
  )
  private AssignAssembly assignAssembly;

  @ManyToOne
  @JoinColumn(
      name = "assemblyLine_id"
  )
  private AssemblyLine assemblyLine;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "manufacture_id"
  )
  private Manufacture manufacture;

  @ManyToOne
  @JoinColumn(
      name = "distributor_id"
  )
  private Distributor distributor;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Factory getFactory() {
    return this.factory;
  }

  public void setFactory(Factory factory) {
    this.factory = factory;
  }

  public Product getProduct() {
    return this.product;
  }

  public void setProduct(Product product) {
    this.product = product;
  }

  public Batch getBatch() {
    return this.batch;
  }

  public void setBatch(Batch batch) {
    this.batch = batch;
  }

  @Enumerated(EnumType.STRING)
  public InspectionStatus getInspectionStatus() {
    return this.inspectionstatus;
  }

  public void setInspectionStatus(InspectionStatus inspectionstatus) {
    this.inspectionstatus = inspectionstatus;
  }

  @Enumerated(EnumType.STRING)
  public InspectionResult getInspectionResult() {
    return this.inspectionresult;
  }

  public void setInspectionResult(InspectionResult inspectionresult) {
    this.inspectionresult = inspectionresult;
  }

  @Enumerated(EnumType.STRING)
  public InspectionType getInspectionType() {
    return this.inspectiontype;
  }

  public void setInspectionType(InspectionType inspectiontype) {
    this.inspectiontype = inspectiontype;
  }

  @Enumerated(EnumType.STRING)
  public InspectionActionCriteria getInspectionActionCriteria() {
    return this.inspectionactioncriteria;
  }

  public void setInspectionActionCriteria(InspectionActionCriteria inspectionactioncriteria) {
    this.inspectionactioncriteria = inspectionactioncriteria;
  }

  public AppUser getAssignee() {
    return this.assignee;
  }

  public void setAssignee(AppUser assignee) {
    this.assignee = assignee;
  }

  public AppUser getInspector() {
    return this.inspector;
  }

  public void setInspector(AppUser inspector) {
    this.inspector = inspector;
  }

  public AssignAssembly getAssignAssembly() {
    return this.assignAssembly;
  }

  public void setAssignAssembly(AssignAssembly assignAssembly) {
    this.assignAssembly = assignAssembly;
  }

  public AssemblyLine getAssemblyLine() {
    return this.assemblyLine;
  }

  public void setAssemblyLine(AssemblyLine assemblyLine) {
    this.assemblyLine = assemblyLine;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public Manufacture getManufacture() {
    return this.manufacture;
  }

  public void setManufacture(Manufacture manufacture) {
    this.manufacture = manufacture;
  }

  public Distributor getDistributor() {
    return this.distributor;
  }

  public void setDistributor(Distributor distributor) {
    this.distributor = distributor;
  }
}
