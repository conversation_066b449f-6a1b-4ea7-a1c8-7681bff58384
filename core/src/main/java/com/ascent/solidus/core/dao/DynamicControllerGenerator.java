package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.FieldConfig;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.squareup.javapoet.*;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.lang.model.element.Modifier;
import java.io.File;
import java.io.IOException;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class DynamicControllerGenerator {
    private static final String CONTROLLER_PACKAGE = "com.ascent.solidus.web.controller";
    private static final String OUTPUT_DIR = "../web/src/main/java";
    private static final String SERVICE_PACKAGE = "com.ascent.solidus.services";
    private static final String ENTITY_PACKAGE = "com.ascent.solidus.core.domain.module";

    public JavaFile generateControllerClass(Class<?> entityClass, ModuleConfig moduleConfig) throws IOException {
        String entityName = entityClass.getSimpleName();
        String controllerName = entityName + "Controller";
        String serviceName = entityName + "Service";
        String basePath = "/api/" + entityName.toLowerCase();

        // Field for service
        FieldSpec serviceField = FieldSpec.builder(
                        ClassName.get(SERVICE_PACKAGE, serviceName),
                        "service",
                        Modifier.PRIVATE, Modifier.FINAL)
                .build();

        // Constructor
        MethodSpec constructor = MethodSpec.constructorBuilder()
                .addModifiers(Modifier.PUBLIC)
                .addParameter(ClassName.get(SERVICE_PACKAGE, serviceName), "service")
                .addStatement("this.service = service")
                .build();

        // Build standard CRUD endpoints
        TypeSpec.Builder controllerClassBuilder = TypeSpec.classBuilder(controllerName)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(RestController.class)
                .addAnnotation(AnnotationSpec.builder(RequestMapping.class)
                        .addMember("value", "$S", basePath)
                        .build())
                .addField(serviceField)
                .addMethod(constructor)
                .addMethod(generateGetAllMethod(entityClass))
                .addMethod(generateGetByIdMethod(entityClass))
                .addMethod(generateCreateMethod(entityClass))
                .addMethod(generateUpdateMethod(entityClass))
                .addMethod(generateDeleteMethod(entityClass));

        // Add reference field endpoints
        if (moduleConfig.getFields() != null) {
            List<FieldConfig> referenceFields = moduleConfig.getFields().stream()
                    .filter(f -> "reference".equalsIgnoreCase(f.getFieldType()))
                    .filter(f -> !f.getTransient() && f.getValid())
                    .collect(Collectors.toList());

            for (FieldConfig refField : referenceFields) {
                String refEntityName = getValidEntityName(refField.getReferenceModule());
                String fieldName = refField.getFieldName();

                // Add endpoint to get entities by reference ID
//                controllerClassBuilder.addMethod(generateGetByReferenceMethod(
//                        entityName,
//                        refEntityName,
//                        fieldName));

                // Add endpoint to get referenced entity from current entity
                controllerClassBuilder.addMethod(generateGetReferencedEntityMethod(
                        entityName,
                        refEntityName,
                        fieldName));
            }
        }

        // Add dependency endpoints (reverse references)
//        if (moduleConfig.getDependencies() != null && !moduleConfig.getDependencies().isEmpty()) {
//            for (String dependency : moduleConfig.getDependencies()) {
//                String refEntityName = getValidEntityName(dependency);
//
//                // Add endpoint to find entities that reference this entity
//                controllerClassBuilder.addMethod(generateGetReferencingEntitiesMethod(
//                        entityName,
//                        refEntityName));
//            }
//        }

        if (moduleConfig.getDependencies() != null && !moduleConfig.getDependencies().isEmpty()) {
            for (String dependency : moduleConfig.getDependencies()) {
                String refEntityName = getValidEntityName(dependency);

                // Find the corresponding field config for this dependency
                Optional<FieldConfig> fieldConfig = moduleConfig.getFields().stream()
                        .filter(f -> "reference".equalsIgnoreCase(f.getFieldType()))
                        .filter(f -> refEntityName.equals(getValidEntityName(f.getReferenceModule())))
                        .findFirst();

                // If no field config found, field is transient, or field is not valid, skip
                if (fieldConfig.isEmpty() || fieldConfig.get().getTransient() || !fieldConfig.get().getValid()) {
                    continue;
                }

                // Add method to find entities that reference this entity
                generateGetReferencingEntitiesMethod(
                        entityName,
                        refEntityName,
                        fieldConfig.get()).ifPresent(controllerClassBuilder::addMethod);
            }
        }

        // Build Java file and write to disk
        JavaFile javaFile = JavaFile.builder(CONTROLLER_PACKAGE, controllerClassBuilder.build())
                .build();
        javaFile.writeTo(new File(OUTPUT_DIR));
        return javaFile;
    }

    private MethodSpec generateGetAllMethod(Class<?> entityClass) {
        ClassName pageType = ClassName.get("org.springframework.data.domain", "Page");
        ClassName pageableType = ClassName.get("org.springframework.data.domain", "Pageable");
        ClassName responseEntity = ClassName.get("org.springframework.http", "ResponseEntity");

        return MethodSpec.methodBuilder("getAll")
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(GetMapping.class)
                .returns(ParameterizedTypeName.get(responseEntity, pageType))
                .addParameter(
                        ParameterSpec.builder(
                                        ParameterizedTypeName.get(
                                                ClassName.get("java.util", "Map"),
                                                ClassName.get(String.class),
                                                ClassName.get(Object.class)),
                                        "filters")
                                .addAnnotation(RequestParam.class)
                                .addAnnotation(AnnotationSpec.builder(Parameter.class)
                                        .addMember("required", "$L", false)
                                        .build())
                                .build())
                .addParameter(pageableType, "pageable")
                .addStatement("return ResponseEntity.ok(service.findAll(filters, pageable))")
                .build();
    }

    private MethodSpec generateGetByIdMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("getById")
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(GetMapping.class)
                        .addMember("value", "$S", "/{id}")
                        .build())
                .returns(ClassName.get(entityClass))
                .addParameter(
                        ParameterSpec.builder(String.class, "id")
                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
                                        .addMember("value", "$S", "id")
                                        .build())
                                .build())
                .addStatement("return service.findById(id)")
                .build();
    }

    private MethodSpec generateCreateMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("create")
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(PostMapping.class)
                .returns(ClassName.get(entityClass))
                .addParameter(
                        ParameterSpec.builder(ClassName.get(entityClass), "entity")
                                .addAnnotation(RequestBody.class)
                                .build())
                .addStatement("return service.save(entity)")
                .build();
    }

    private MethodSpec generateUpdateMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("update")
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(PutMapping.class)
                        .addMember("value", "$S", "/{id}")
                        .build())
                .returns(ClassName.get(entityClass))
                .addParameter(
                        ParameterSpec.builder(String.class, "id")
                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
                                        .addMember("value", "$S", "id")
                                        .build())
                                .build())
                .addParameter(
                        ParameterSpec.builder(ClassName.get(entityClass), "entity")
                                .addAnnotation(RequestBody.class)
                                .build())
                .addStatement("entity.setId(id)")
                .addStatement("return service.save(entity)")
                .build();
    }

    private MethodSpec generateDeleteMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("delete")
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(DeleteMapping.class)
                        .addMember("value", "$S", "/{id}")
                        .build())
                .addParameter(
                        ParameterSpec.builder(String.class, "id")
                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
                                        .addMember("value", "$S", "id")
                                        .build())
                                .build())
                .addStatement("service.delete(id)")
                .build();
    }

//    private MethodSpec generateGetByReferenceMethod(String entityName, String refEntityName, String fieldName) {
//        String methodName = "getBy" + capitalize(fieldName);
//        String paramName = fieldName.toLowerCase() + "Id";
//        String path = "/by-" + fieldName.toLowerCase() + "/{" + paramName + "}";
//
//        return MethodSpec.methodBuilder(methodName)
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(AnnotationSpec.builder(GetMapping.class)
//                        .addMember("value", "$S", path)
//                        .build())
//                .returns(ParameterizedTypeName.get(
//                        ClassName.get("org.springframework.http", "ResponseEntity"),
//                        ParameterizedTypeName.get(
//                                ClassName.get("java.util", "List"),
//                                ClassName.get(ENTITY_PACKAGE, entityName))))
//                .addParameter(
//                        ParameterSpec.builder(String.class, paramName)
//                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
//                                        .addMember("value", "$S", paramName)
//                                        .build())
//                                .build())
//                .addStatement("return ResponseEntity.ok(service.findBy" + capitalize(fieldName) + "(" + paramName + "))")
//                .build();
//    }

    private MethodSpec generateGetReferencedEntityMethod(String entityName, String refEntityName, String fieldName) {
        String methodName = "get" + capitalize(refEntityName) + "For" + entityName;
        String path = "/{id}/" + fieldName.toLowerCase();

        return MethodSpec.methodBuilder(methodName)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(GetMapping.class)
                        .addMember("value", "$S", path)
                        .build())
                .returns(ParameterizedTypeName.get(
                        ClassName.get("org.springframework.http", "ResponseEntity"),
                        ClassName.get(ENTITY_PACKAGE, refEntityName))) // Direct entity type
                .addParameter(
                        ParameterSpec.builder(String.class, "id")
                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
                                        .addMember("value", "$S", "id")
                                        .build())
                                .build())
                .addStatement("$T entity = service.get" + capitalize(refEntityName) + "By" + entityName + "Id(id)",
                        ClassName.get(ENTITY_PACKAGE, refEntityName))
                .addStatement("return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build()")
                .build();
    }

    private Optional<MethodSpec> generateGetReferencingEntitiesMethod(String entityName, String refEntityName,FieldConfig fieldConfig) {
        String methodName = "get" + capitalize(refEntityName) + "sReferencing" + entityName;
        String paramName = entityName.toLowerCase() + "Id";
        String path = "/{" + paramName + "}/referencing-" + refEntityName.toLowerCase() + "s";

        if (fieldConfig.getTransient() || !fieldConfig.getValid()) {
            return Optional.empty();
        }

        return Optional.of(MethodSpec.methodBuilder(methodName)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(GetMapping.class)
                        .addMember("value", "$S", path)
                        .build())
                .returns(ParameterizedTypeName.get(
                        ClassName.get("org.springframework.http", "ResponseEntity"),
                        ParameterizedTypeName.get(
                                ClassName.get("java.util", "List"),
                                ClassName.get(ENTITY_PACKAGE, entityName))))
                .addParameter(
                        ParameterSpec.builder(String.class, paramName)
                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
                                        .addMember("value", "$S", paramName)
                                        .build())
                                .build())
                .addStatement("return ResponseEntity.ok(service.find"+ refEntityName +"By" + entityName + "Id(" + paramName + "))")
                .build());
    }

    private String getValidEntityName(String referenceModule) {
        if (referenceModule.matches("^\\d.*")) {
            return "Module" + referenceModule;
        }
        return referenceModule;
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}