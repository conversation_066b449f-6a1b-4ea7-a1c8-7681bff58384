package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.FieldConfig;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.ascent.solidus.core.domain.module.ParentModule;
import com.squareup.javapoet.*;
import jakarta.validation.Valid;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.stereotype.Component;
import jakarta.persistence.*;
import javax.lang.model.element.Modifier;
import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


@Component
public class DynamicEntityGenerator {
    private static final String BASE_PACKAGE = "com.ascent.solidus.core.domain.module";
    private static final String OUTPUT_DIR = "src/main/java";
    private static final String ENUM_PACKAGE="com.ascent.solidus.core.constants";

    public JavaFile generateEntityClass(ModuleConfig moduleConfig) throws IOException, ClassNotFoundException {
        String className = moduleConfig.getModuleName();

//        for (FieldConfig field : moduleConfig.getFields()) {
//            if ("enum".equalsIgnoreCase(field.getFieldType())) {
//                new DynamicEnumGenerator().generateEnumClass(field);
//            }
//        }

        TypeSpec.Builder classBuilder = TypeSpec.classBuilder(className)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(Entity.class)
                .addAnnotation(AnnotationSpec.builder(Table.class)
                        .addMember("name", "$S", className.toLowerCase())
                        .build());

        // Add ID field
        addIdField(classBuilder);

        // Add parent module relationship if exists
        if (moduleConfig.getParentModuleId() != null) {
            addParentModuleField(classBuilder, moduleConfig.getParentModule());
        }

        // Add dynamic fields
        for (FieldConfig field : moduleConfig.getFields()) {
            addFieldWithAccessors(classBuilder, field);
        }

        // Build and write the Java file
        JavaFile javaFile = JavaFile.builder(BASE_PACKAGE, classBuilder.build())
                .build();
        javaFile.writeTo(new File(OUTPUT_DIR));
        return javaFile;
    }

    private void addIdField(TypeSpec.Builder classBuilder) {
        // Convert String.class to TypeName
        TypeName stringType = TypeName.get(String.class);

        FieldSpec idField = FieldSpec.builder(stringType, "id", Modifier.PRIVATE)
                .addAnnotation(Id.class)
                .addAnnotation(AnnotationSpec.builder(GeneratedValue.class)
                        .addMember("generator", "$S", "uuid")
                        .build())
                .addAnnotation(AnnotationSpec.builder(GenericGenerator.class)
                        .addMember("name", "$S", "uuid")
                        .addMember("strategy", "$S", "uuid2")
                        .build())
                .build();

        classBuilder.addField(idField)
                .addMethod(createGetter(stringType, "id"))  // Now passing TypeName
                .addMethod(createSetter("id", stringType)); // Now passing TypeName
    }

//    private void addParentModuleField(TypeSpec.Builder classBuilder, ParentModule parentModule) {
//        String parentField = "parentModule";
//
//        // Use ClassName.get() to get the proper reference for the ParentModule class
//        ClassName parentModuleType = ClassName.get(BASE_PACKAGE, parentModule.getClass().getSimpleName());
//
//        // Add ManyToOne relationship
//        FieldSpec parentModuleField = FieldSpec.builder(
//                        parentModuleType,
//                        parentField,
//                        Modifier.PRIVATE)
//                .addAnnotation(ManyToOne.class)
//                .addAnnotation(AnnotationSpec.builder(JoinColumn.class)
//                        .addMember("name", "$S", "parent_module_id")
//                        .addMember("referencedColumnName", "$S", "id")
//                        .build())
//                .build();
//
//        // Add the field and getter/setter methods
//        classBuilder.addField(parentModuleField)
//                .addMethod(createGetter(parentModuleType, parentField))
//                .addMethod(createSetter(parentField, parentModuleType));
//    }
private void addParentModuleField(TypeSpec.Builder classBuilder, ParentModule parentModule) throws ClassNotFoundException {
    String parentField = "parentModule";

    // Get the proper TypeName for ParentModule
    ClassName parentModuleType = ClassName.get(BASE_PACKAGE, parentModule.getClass().getSimpleName());

    // Add ManyToOne relationship
    FieldSpec parentModuleField = FieldSpec.builder(
                    parentModuleType,
                    parentField,
                    Modifier.PRIVATE)
            .addAnnotation(ManyToOne.class)
            .addAnnotation(AnnotationSpec.builder(JoinColumn.class)
                    .addMember("name", "$S", "parent_module_id")
                    .addMember("referencedColumnName", "$S", "moduleId")
                    .build())
            .build();

    // Add the field and getter/setter methods using TypeName
    classBuilder.addField(parentModuleField)
            .addMethod(createGetter(parentModuleType, parentField))
            .addMethod(createSetter(parentField, parentModuleType));
}


private void addFieldWithAccessors(TypeSpec.Builder classBuilder, FieldConfig field) {
    String fieldName = field.getFieldName();
    String fieldType = field.getFieldType();
    if ("enum".equalsIgnoreCase(fieldType)) {
        addEnumField(classBuilder, field);
        return;
    }
    boolean isSingleReference = "reference".equals(fieldType);
    boolean isCollectionReference = fieldType.startsWith("reference_");

    if (isSingleReference) {
        ClassName referencedEntity = ClassName.get(BASE_PACKAGE, field.getReferenceModule());
        FieldSpec.Builder fieldBuilder = FieldSpec.builder(referencedEntity, fieldName, Modifier.PRIVATE);

        if (field.getValid()) {
            // Only @Valid, no JPA annotations
            fieldBuilder.addAnnotation(Valid.class);
        } else if (field.getTransient()) {
            // Only @Transient, no JPA annotations
            fieldBuilder.addAnnotation(Transient.class);
        } else {
            // Normal @ManyToOne with join column
            fieldBuilder
                    .addAnnotation(ManyToOne.class)
                    .addAnnotation(AnnotationSpec.builder(JoinColumn.class)
                            .addMember("name", "$S", fieldName + "_id")
                            .build());
        }

        classBuilder.addField(fieldBuilder.build())
                .addMethod(createGetter(referencedEntity, fieldName))
                .addMethod(createSetter(fieldName, referencedEntity));
    }
    else if (isCollectionReference) {
        ClassName referencedEntity = ClassName.get(BASE_PACKAGE, field.getReferenceModule());
        TypeName listType = ParameterizedTypeName.get(ClassName.get(List.class), referencedEntity);
        FieldSpec.Builder fieldBuilder = FieldSpec.builder(listType, fieldName, Modifier.PRIVATE);

        MethodSpec.Builder getterBuilder = MethodSpec.methodBuilder("get" + capitalize(fieldName))
                .addModifiers(Modifier.PUBLIC)
                .returns(listType)
                .addStatement("return this." + fieldName);

        if(field.getValid()){
            fieldBuilder.addAnnotation(Valid.class);
        }
        if (field.getTransient()) {
            // Only @Transient, no JPA annotations
            fieldBuilder.addAnnotation(Transient.class);
        }
        else {
            // Normal @OneToMany with join table
            fieldBuilder
                    .addAnnotation(OneToMany.class)
                    .addAnnotation(AnnotationSpec.builder(JoinTable.class)
                            .addMember("name", "$S", fieldName + "_mapping")
                            .build());
        }

        classBuilder.addField(fieldBuilder.build())
                .addMethod(getterBuilder.build())
                .addMethod(createSetter(fieldName, listType))
//                .addMethod(MethodSpec.constructorBuilder()
//                        .addStatement("this.$L = new $T<>()", fieldName, ArrayList.class)
                        .build();
    }
}

    private void addEnumField(TypeSpec.Builder classBuilder, FieldConfig field) {
        String fieldName = field.getFieldName();
        String enumName = capitalize(fieldName);
        ClassName enumType = ClassName.get(ENUM_PACKAGE, enumName);

        // Create the field with @Enumerated annotation
        FieldSpec enumField = FieldSpec.builder(enumType, fieldName.toLowerCase(), Modifier.PRIVATE)
                .build();

        // Create getter with @Enumerated(EnumType.STRING)
        MethodSpec getter = MethodSpec.methodBuilder("get" + capitalize(fieldName))
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(AnnotationSpec.builder(Enumerated.class)
                        .addMember("value", "$T.STRING", EnumType.class)
                        .build())
                .returns(enumType)
                .addStatement("return this." + fieldName.toLowerCase())
                .build();

        // Create setter
        MethodSpec setter = MethodSpec.methodBuilder("set" + capitalize(fieldName))
                .addModifiers(Modifier.PUBLIC)
                .addParameter(enumType, fieldName.toLowerCase())
                .addStatement("this." + fieldName.toLowerCase() + " = " + fieldName.toLowerCase())
                .build();

        classBuilder.addField(enumField)
                .addMethod(getter)
                .addMethod(setter);
    }



    // Helper method to format class names from module IDs
    private String formatClassName(String moduleId) {
        return Arrays.stream(moduleId.split("_"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
                .collect(Collectors.joining("_"));
    }

    private MethodSpec createGetter(TypeName returnType, String fieldName) {
        return MethodSpec.methodBuilder("get" + capitalize(fieldName))
                .addModifiers(Modifier.PUBLIC)
                .returns(returnType)
                .addStatement("return this." + fieldName)
                .build();
    }

    private MethodSpec createSetter(String fieldName, TypeName paramType) {
        return MethodSpec.methodBuilder("set" + capitalize(fieldName))
                .addModifiers(Modifier.PUBLIC)
                .addParameter(paramType, fieldName)
                .addStatement("this." + fieldName + " = " + fieldName)
                .build();
    }




    private Class<?> getJavaType(String fieldType) {
        return switch (fieldType.toLowerCase()) {
            case "text", "string","String" -> String.class;
            case "number" -> Integer.class;
            case "date" -> Date.class;
            case "boolean" -> Boolean.class;
            case "long" -> Long.class;
            case "double" -> Double.class;
            case "float" -> Float.class;
            default -> Object.class;
        };
    }

    private String convertToFieldName(String displayName) {
        return displayName.toLowerCase().replace(" ", "_");
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
