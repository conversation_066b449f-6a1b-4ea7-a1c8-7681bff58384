package com.ascent.solidus.core.domain.module;

public class AggregationConfig {
    private String function;  // "COUNT", "SUM", "AVG", "MIN", "MAX"
    private String field;
    private String alias;

    // Constructors
    public AggregationConfig() {}

    public AggregationConfig(String function, String field, String alias) {
        this.function = function;
        this.field = field;
        this.alias = alias;
    }

    // Getters and Setters
    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    @Override
    public String toString() {
        return "AggregationConfig{" +
                "function='" + function + '\'' +
                ", field='" + field + '\'' +
                ", alias='" + alias + '\'' +
                '}';
    }
}