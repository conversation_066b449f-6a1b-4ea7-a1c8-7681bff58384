package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.domain.umgmt.Role;
import com.ascent.solidus.core.constants.RoleName;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Optional<Role> findByName(RoleName name);
}