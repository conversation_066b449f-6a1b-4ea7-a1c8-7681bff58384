package com.ascent.solidus.core.domain.module;

import java.util.List;

public class SubmoduleConfig {
    private String moduleId;
    private String moduleName;
    private List<FieldConfig> fields;

    // Constructors
    public SubmoduleConfig() {}

    public SubmoduleConfig(String moduleId, String moduleName, List<FieldConfig> fields) {
        this.moduleId = moduleId;
        this.moduleName = moduleName;
        this.fields = fields;
    }

    // Getters and Setters
    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public List<FieldConfig> getFields() {
        return fields;
    }

    public void setFields(List<FieldConfig> fields) {
        this.fields = fields;
    }
}
