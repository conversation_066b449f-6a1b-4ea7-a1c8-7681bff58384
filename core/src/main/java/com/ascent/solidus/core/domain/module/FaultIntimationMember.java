package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "faultintimationmember"
)
public class FaultIntimationMember {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "member_id"
  )
  private AppUser member;

  @ManyToOne
  @JoinColumn(
      name = "faultIntimationTeam_id"
  )
  private FaultIntimationTeam faultIntimationTeam;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public AppUser getMember() {
    return this.member;
  }

  public void setMember(AppUser member) {
    this.member = member;
  }

  public FaultIntimationTeam getFaultIntimationTeam() {
    return this.faultIntimationTeam;
  }

  public void setFaultIntimationTeam(FaultIntimationTeam faultIntimationTeam) {
    this.faultIntimationTeam = faultIntimationTeam;
  }
}
