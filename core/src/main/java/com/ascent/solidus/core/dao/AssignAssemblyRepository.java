package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.AssemblyInspectionRound;
import com.ascent.solidus.core.domain.module.AssignAssembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Stage;
import java.lang.String;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AssignAssemblyRepository extends JpaRepository<AssignAssembly, String>, JpaSpecificationExecutor<AssignAssembly> {
  @Query("SELECT e.assembly FROM AssignAssembly e WHERE e.id = :id")
  Assembly getAssemblyByAssignAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM AssignAssembly r WHERE r.assembly.id = :assemblyId")
  List<AssignAssembly> findAssemblyByAssignAssemblyId(@Param("assemblyId") String assemblyId);

  @Query("SELECT e.product FROM AssignAssembly e WHERE e.id = :id")
  Product getProductByAssignAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM AssignAssembly r WHERE r.product.id = :productId")
  List<AssignAssembly> findProductByAssignAssemblyId(@Param("productId") String productId);

  @Query("SELECT e.assemblyInspectionRound FROM AssignAssembly e WHERE e.id = :id")
  AssemblyInspectionRound getAssemblyInspectionRoundByAssignAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM AssignAssembly r WHERE r.assemblyInspectionRound.id = :assemblyinspectionroundId")
  List<AssignAssembly> findAssemblyInspectionRoundByAssignAssemblyId(
      @Param("assemblyinspectionroundId") String assemblyinspectionroundId);

  @Query("SELECT e.stage FROM AssignAssembly e WHERE e.id = :id")
  Stage getStageByAssignAssemblyId(@Param("id") String id);

  @Query("SELECT r FROM AssignAssembly r WHERE r.stage.id = :stageId")
  List<AssignAssembly> findStageByAssignAssemblyId(@Param("stageId") String stageId);
}
