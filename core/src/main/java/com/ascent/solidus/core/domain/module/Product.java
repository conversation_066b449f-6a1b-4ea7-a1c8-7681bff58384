package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "product"
)
public class Product {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @Column(
      name = "name",
      nullable = false
  )
  private String name;

  @Column(
      name = "drawingNumber",
      nullable = true
  )
  private String drawingNumber;

  @Column(
      name = "path",
      nullable = true
  )
  private String path;

  @ManyToOne
  @JoinColumn(
      name = "productFamily_id",
      referencedColumnName = "id"
  )
  private ProductFamily productFamily;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public String getName() {
    return this.name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDrawingNumber() {
    return this.drawingNumber;
  }

  public void setDrawingNumber(String drawingNumber) {
    this.drawingNumber = drawingNumber;
  }

  public String getPath() {
    return this.path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public ProductFamily getProductFamily() {
    return this.productFamily;
  }

  public void setProductFamily(ProductFamily productFamily) {
    this.productFamily = productFamily;
  }
}
