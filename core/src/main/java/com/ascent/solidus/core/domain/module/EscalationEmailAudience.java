package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "escalationemailaudience"
)
public class EscalationEmailAudience {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "escalationEmailTemplate_id"
  )
  private EscalationEmailTemplate escalationEmailTemplate;

  @ManyToOne
  @JoinColumn(
      name = "audience_id"
  )
  private AppUser audience;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public EscalationEmailTemplate getEscalationEmailTemplate() {
    return this.escalationEmailTemplate;
  }

  public void setEscalationEmailTemplate(EscalationEmailTemplate escalationEmailTemplate) {
    this.escalationEmailTemplate = escalationEmailTemplate;
  }

  public AppUser getAudience() {
    return this.audience;
  }

  public void setAudience(AppUser audience) {
    this.audience = audience;
  }
}
