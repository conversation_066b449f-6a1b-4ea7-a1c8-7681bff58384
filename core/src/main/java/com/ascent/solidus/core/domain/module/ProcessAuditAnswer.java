package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.ProcessAuditStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "processauditanswer"
)
public class ProcessAuditAnswer {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "question_id"
  )
  private Question question;

  @ManyToOne
  @JoinColumn(
      name = "auditArea_id"
  )
  private AuditArea auditArea;

  @ManyToOne
  @JoinColumn(
      name = "nonConformance_id"
  )
  private NonConformance nonConformance;

  @ManyToOne
  @JoinColumn(
      name = "processAudit_id"
  )
  private ProcessAudit processAudit;

  @ManyToOne
  @JoinColumn(
      name = "checkListQuestion_id"
  )
  private CheckListQuestion checkListQuestion;

  private ProcessAuditStatus processauditstatus;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Question getQuestion() {
    return this.question;
  }

  public void setQuestion(Question question) {
    this.question = question;
  }

  public AuditArea getAuditArea() {
    return this.auditArea;
  }

  public void setAuditArea(AuditArea auditArea) {
    this.auditArea = auditArea;
  }

  public NonConformance getNonConformance() {
    return this.nonConformance;
  }

  public void setNonConformance(NonConformance nonConformance) {
    this.nonConformance = nonConformance;
  }

  public ProcessAudit getProcessAudit() {
    return this.processAudit;
  }

  public void setProcessAudit(ProcessAudit processAudit) {
    this.processAudit = processAudit;
  }

  public CheckListQuestion getCheckListQuestion() {
    return this.checkListQuestion;
  }

  public void setCheckListQuestion(CheckListQuestion checkListQuestion) {
    this.checkListQuestion = checkListQuestion;
  }

  @Enumerated(EnumType.STRING)
  public ProcessAuditStatus getProcessAuditStatus() {
    return this.processauditstatus;
  }

  public void setProcessAuditStatus(ProcessAuditStatus processauditstatus) {
    this.processauditstatus = processauditstatus;
  }
}
