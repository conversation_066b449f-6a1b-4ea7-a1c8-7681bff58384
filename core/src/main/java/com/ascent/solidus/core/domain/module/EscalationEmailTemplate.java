package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.Valid;
import java.lang.String;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "escalationemailtemplate"
)
public class EscalationEmailTemplate {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "escalationConfiguration_id"
  )
  private EscalationConfiguration escalationConfiguration;

  @Valid
  @Transient
  private List<AppUser> audiences;

  EscalationEmailTemplate() {
    this.audiences = new ArrayList<>();
  }

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public EscalationConfiguration getEscalationConfiguration() {
    return this.escalationConfiguration;
  }

  public void setEscalationConfiguration(EscalationConfiguration escalationConfiguration) {
    this.escalationConfiguration = escalationConfiguration;
  }

  public List<AppUser> getAudiences() {
    return this.audiences;
  }

  public void setAudiences(List<AppUser> audiences) {
    this.audiences = audiences;
  }
}
