package com.ascent.solidus.core.dao;

import com.ascent.solidus.core.domain.module.FieldConfig;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.squareup.javapoet.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.lang.model.element.Modifier;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class DynamicServiceGenerator {
    private static final String SERVICE_PACKAGE = "com.ascent.solidus.services";
    private static final String OUTPUT_DIR = "../services/src/main/java";
    private static final String REPOSITORY_PACKAGE = "com.ascent.solidus.core.dao";
    private static final String ENTITY_PACKAGE = "com.ascent.solidus.core.domain.module";

    public JavaFile generateServiceClass(Class<?> entityClass, ModuleConfig moduleConfig) throws IOException {
        String entityName = entityClass.getSimpleName();
        String serviceName = entityName + "Service";
        String repositoryName = entityName + "Repository";

        // Field for repository
        FieldSpec repositoryField = FieldSpec.builder(
                        ClassName.get(REPOSITORY_PACKAGE, repositoryName),
                        "repository",
                        Modifier.PRIVATE, Modifier.FINAL)
                .build();

        // Constructor
        MethodSpec constructor = MethodSpec.constructorBuilder()
                .addModifiers(Modifier.PUBLIC)
                .addParameter(ClassName.get(REPOSITORY_PACKAGE, repositoryName), "repository")
                .addStatement("this.repository = repository")
                .build();

        // Build the class with basic CRUD methods
        TypeSpec.Builder serviceClassBuilder = TypeSpec.classBuilder(serviceName)
                .addModifiers(Modifier.PUBLIC)
                .addAnnotation(Service.class)
                .addField(repositoryField)
                .addMethod(constructor)
                .addMethod(generateFindAllMethod(entityClass))
                .addMethod(generateFindByIdMethod(entityClass))
                .addMethod(generateSaveMethod(entityClass))
                .addMethod(generateDeleteMethod(entityClass));

        // Add reference field methods if any
        if (moduleConfig.getFields() != null) {
            List<FieldConfig> referenceFields = moduleConfig.getFields().stream()
                    .filter(f -> "reference".equalsIgnoreCase(f.getFieldType()))
                    .filter(f -> !f.getTransient() && f.getValid())
                    .collect(Collectors.toList());

            for (FieldConfig refField : referenceFields) {
                String refEntityName = getValidEntityName(refField.getReferenceModule());
                String fieldName = refField.getFieldName();

                // Add method to find by reference ID
//                serviceClassBuilder.addMethod(generateFindByReferenceMethod(
//                        entityName,
//                        refEntityName,
//                        fieldName));

                // Add method to get referenced entity
                serviceClassBuilder.addMethod(generateGetReferencedEntityMethod(
                        entityName,
                        refEntityName,
                        fieldName));
            }
        }

        // Add methods for dependencies (reverse references)
//        if (moduleConfig.getDependencies() != null && !moduleConfig.getDependencies().isEmpty()) {
//            for (String dependency : moduleConfig.getDependencies()) {
//                String refEntityName = getValidEntityName(dependency);
//
//
//                // Add method to find entities that reference this entity
//                serviceClassBuilder.addMethod(generateFindReferencingEntitiesMethod(
//                        entityName,
//                        refEntityName));
//            }
//        }

        if (moduleConfig.getDependencies() != null && !moduleConfig.getDependencies().isEmpty()) {
            for (String dependency : moduleConfig.getDependencies()) {
                String refEntityName = getValidEntityName(dependency);

                // Find the corresponding field config for this dependency
                Optional<FieldConfig> fieldConfig = moduleConfig.getFields().stream()
                        .filter(f -> "reference".equalsIgnoreCase(f.getFieldType()))
                        .filter(f -> refEntityName.equals(getValidEntityName(f.getReferenceModule())))
                        .findFirst();

                // If no field config found, field is transient, or field is not valid, skip
                if (fieldConfig.isEmpty() || fieldConfig.get().getTransient() || !fieldConfig.get().getValid()) {
                    continue;
                }

                // Add method to find entities that reference this entity
                generateFindReferencingEntitiesMethod(
                        entityName,
                        refEntityName,
                        fieldConfig.get()).ifPresent(serviceClassBuilder::addMethod);
            }
        }

        // Build Java file and write to disk
        JavaFile javaFile = JavaFile.builder(SERVICE_PACKAGE, serviceClassBuilder.build())
                .build();
        javaFile.writeTo(new File(OUTPUT_DIR));
        return javaFile;
    }

    private MethodSpec generateFindAllMethod(Class<?> entityClass) {
        ClassName pageType = ClassName.get("org.springframework.data.domain", "Page");
        ClassName pageableType = ClassName.get("org.springframework.data.domain", "Pageable");
        ClassName specType = ClassName.get("org.springframework.data.jpa.domain", "Specification");
        ClassName predicates = ClassName.get("com.ascent.solidus.core.util", "PredicateBuilder");

        return MethodSpec.methodBuilder("findAll")
                .addModifiers(Modifier.PUBLIC)
                .returns(ParameterizedTypeName.get(pageType, ClassName.get(entityClass)))
                .addParameter(
                        ParameterSpec.builder(
                                        ParameterizedTypeName.get(
                                                ClassName.get("java.util", "Map"),
                                                ClassName.get(String.class),
                                                ClassName.get(Object.class)),
                                        "filters")
                                .build())
                .addParameter(pageableType, "pageable")
                .addStatement("$T<$T> spec = $T.buildSpecification(filters)",
                        specType, ClassName.get(entityClass), predicates)
                .addStatement("return repository.findAll(spec, pageable)")
                .build();
    }

    private MethodSpec generateFindByIdMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("findById")
                .addModifiers(Modifier.PUBLIC)
                .returns(ClassName.get(entityClass))
                .addParameter(String.class, "id")
                .addStatement("return repository.findById(id).orElseThrow(() -> new RuntimeException(\"Not found\"))")
                .build();
    }

    private MethodSpec generateSaveMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("save")
                .addModifiers(Modifier.PUBLIC)
                .returns(ClassName.get(entityClass))
                .addParameter(ClassName.get(entityClass), "entity")
                .addStatement("return repository.save(entity)")
                .build();
    }

    private MethodSpec generateDeleteMethod(Class<?> entityClass) {
        return MethodSpec.methodBuilder("delete")
                .addModifiers(Modifier.PUBLIC)
                .addParameter(String.class, "id")
                .addStatement("repository.deleteById(id)")
                .build();
    }

//    private MethodSpec generateFindByReferenceMethod(String entityName, String refEntityName, String fieldName) {
//        return MethodSpec.methodBuilder("findBy" + capitalize(fieldName))
//                .addModifiers(Modifier.PUBLIC)
//                .returns(ParameterizedTypeName.get(
//                        ClassName.get("java.util", "List"),
//                        ClassName.get(ENTITY_PACKAGE, entityName)))
//                .addParameter(ClassName.get(ENTITY_PACKAGE, refEntityName), fieldName)
//                .addStatement("return repository.findBy" + capitalize(fieldName) + "(" + fieldName + ")")
//                .build();
//    }

    private MethodSpec generateGetReferencedEntityMethod(String entityName, String refEntityName, String fieldName) {
        return MethodSpec.methodBuilder("get" + capitalize(refEntityName) + "By" + entityName + "Id")
                .addModifiers(Modifier.PUBLIC)
                .returns(ClassName.get(ENTITY_PACKAGE, refEntityName)) // Direct entity type return
                .addParameter(String.class, "id")
                .addStatement("return repository.get" + capitalize(refEntityName) + "By" + entityName + "Id(id)")
                .build();
    }

    private Optional<MethodSpec> generateFindReferencingEntitiesMethod(String entityName, String refEntityName,FieldConfig fieldConfig) {
        String methodName = "find" + refEntityName + "By" + entityName + "Id";
        String paramName = entityName.toLowerCase() + "Id";

        if (fieldConfig.getTransient() || !fieldConfig.getValid()) {
            return Optional.empty();
        }

        return Optional.of(MethodSpec.methodBuilder(methodName)
                .addModifiers(Modifier.PUBLIC)
                .returns(ParameterizedTypeName.get(
                        ClassName.get("java.util", "List"),
                        ClassName.get(ENTITY_PACKAGE, entityName)))
                .addParameter(String.class, paramName)
                .addStatement("return repository.find" + refEntityName + "By" + entityName + "Id(" + paramName + ")")
                .build());
    }

    private String getValidEntityName(String referenceModule) {
        if (referenceModule.matches("^\\d.*")) {
            return "Module" + referenceModule;
        }
        return referenceModule;
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}