package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "faultintimationdefect"
)
public class FaultIntimationDefect {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "defect_id"
  )
  private SpotBuyDefect defect;

  @ManyToOne
  @JoinColumn(
      name = "faultIntimation_id"
  )
  private FaultIntimation faultIntimation;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public SpotBuyDefect getDefect() {
    return this.defect;
  }

  public void setDefect(SpotBuyDefect defect) {
    this.defect = defect;
  }

  public FaultIntimation getFaultIntimation() {
    return this.faultIntimation;
  }

  public void setFaultIntimation(FaultIntimation faultIntimation) {
    this.faultIntimation = faultIntimation;
  }
}
