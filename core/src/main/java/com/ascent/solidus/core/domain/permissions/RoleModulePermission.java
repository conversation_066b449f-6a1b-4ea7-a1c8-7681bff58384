package com.ascent.solidus.core.domain.permissions;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.AbstractEntity;
import com.ascent.solidus.core.domain.tenant.Tenant;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

/**
 * Entity representing which modules a role can access
 * This is for the role-based permission system shown in the UI
 */
@Entity
@Table(name = "role_module_permissions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_name", "module_name"})
})
public class RoleModulePermission extends AbstractEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "role_name", nullable = false)
    private RoleName roleName;
    
    @Column(name = "module_name", nullable = false)
    private String moduleName; // e.g., "ProductFamily", "Revisions", "Part" (only actual modules, no parents)
    
    @Column(nullable = false)
    private boolean canRead = true;
    
    @Column(nullable = false)
    private boolean canWrite = false;
    
    @Column(nullable = false)
    private boolean canDelete = false;
    
    @Column(nullable = false)
    private boolean canExecute = false;
    
    @Column(nullable = false)
    private boolean active = true;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private Date createdOn;
    
    // Constructors
    public RoleModulePermission() {}

    public RoleModulePermission(RoleName roleName, String moduleName) {
        this.roleName = roleName;
        this.moduleName = moduleName;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public RoleName getRoleName() {
        return roleName;
    }
    
    public void setRoleName(RoleName roleName) {
        this.roleName = roleName;
    }
    
    public String getModuleName() {
        return moduleName;
    }
    
    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
    

    
    public boolean isCanRead() {
        return canRead;
    }
    
    public void setCanRead(boolean canRead) {
        this.canRead = canRead;
    }
    
    public boolean isCanWrite() {
        return canWrite;
    }
    
    public void setCanWrite(boolean canWrite) {
        this.canWrite = canWrite;
    }
    
    public boolean isCanDelete() {
        return canDelete;
    }
    
    public void setCanDelete(boolean canDelete) {
        this.canDelete = canDelete;
    }
    
    public boolean isCanExecute() {
        return canExecute;
    }
    
    public void setCanExecute(boolean canExecute) {
        this.canExecute = canExecute;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public Date getCreatedOn() {
        return createdOn;
    }
    
    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    @Override
    public Tenant getTenant() {
        return null; // Role permissions are global, not tenant-specific initially
    }

    @Override
    public void setTenant(Tenant tenant) {
        // Role permissions are global initially
    }
}
