package com.ascent.solidus.core.domain.umgmt;

import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;

@Entity
@Table(name = "city")
public class City extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    private String name;

    @ManyToOne(fetch = FetchType.LAZY, optional = true)
    @JoinColumn(name = "State_Id", referencedColumnName = "id")
    private State state;

}
