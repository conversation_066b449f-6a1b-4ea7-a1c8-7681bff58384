package com.ascent.solidus.core.domain.module;

import com.ascent.solidus.core.constants.FaultIntimationDecision;
import com.ascent.solidus.core.constants.FaultIntimationStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "faultintimationnote"
)
public class FaultIntimationNote {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  private FaultIntimationStatus faultintimationstatus;

  private FaultIntimationDecision faultintimationdecision;

  @ManyToOne
  @JoinColumn(
      name = "member_id"
  )
  private AppUser member;

  @ManyToOne
  @JoinColumn(
      name = "faultIntimationTeam_id"
  )
  private FaultIntimationTeam faultIntimationTeam;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  @Enumerated(EnumType.STRING)
  public FaultIntimationStatus getFaultIntimationStatus() {
    return this.faultintimationstatus;
  }

  public void setFaultIntimationStatus(FaultIntimationStatus faultintimationstatus) {
    this.faultintimationstatus = faultintimationstatus;
  }

  @Enumerated(EnumType.STRING)
  public FaultIntimationDecision getFaultIntimationDecision() {
    return this.faultintimationdecision;
  }

  public void setFaultIntimationDecision(FaultIntimationDecision faultintimationdecision) {
    this.faultintimationdecision = faultintimationdecision;
  }

  public AppUser getMember() {
    return this.member;
  }

  public void setMember(AppUser member) {
    this.member = member;
  }

  public FaultIntimationTeam getFaultIntimationTeam() {
    return this.faultIntimationTeam;
  }

  public void setFaultIntimationTeam(FaultIntimationTeam faultIntimationTeam) {
    this.faultIntimationTeam = faultIntimationTeam;
  }
}
