package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TenantMappingRepository extends JpaRepository<TenantMapping, Long> {
    List<TenantMapping> findByParentAndActive(Tenant parent, boolean active);
    List<TenantMapping> findByChildAndActive(Tenant child, boolean active);
    
    @Query("SELECT tm FROM TenantMapping tm WHERE tm.parent.id = :tenantId AND tm.active = true")
    List<TenantMapping> findAllChildrenByParentId(Long tenantId);
    
    @Query("SELECT tm FROM TenantMapping tm WHERE tm.child.id = :tenantId AND tm.active = true")
    List<TenantMapping> findAllParentsByChildId(Long tenantId);
}