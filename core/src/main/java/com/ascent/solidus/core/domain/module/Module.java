//package com.ascent.solidus.core.domain.module;
//
//import com.ascent.solidus.core.domain.AbstractEntity;
//import jakarta.persistence.*;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.NoArgsConstructor;
//import lombok.ToString;
//
//import java.util.HashSet;
//import java.util.Set;
//
////@Entity
////@Table(name = "modules")
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//public class Module extends AbstractEntity {
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @Column(nullable = false, unique = true)
//    private String name;
//
//    @Column
//    private String description;
//
//    @Column(nullable = false)
//    private boolean isStandalone = false;
//
//    @Column(nullable = false)
//    private boolean isComponent = false;
//
//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "parent_id")
//    @ToString.Exclude
//    @EqualsAndHashCode.Exclude
//    private Module parent;
//
//    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    @ToString.Exclude
//    @EqualsAndHashCode.Exclude
//    private Set<Module> subModules = new HashSet<>();
//
//    @Column
//    private String icon;
//}
