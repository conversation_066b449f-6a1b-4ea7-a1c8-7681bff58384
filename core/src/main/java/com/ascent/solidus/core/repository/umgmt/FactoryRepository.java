package com.ascent.solidus.core.repository.umgmt;

import com.ascent.solidus.core.constants.FactoryType;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.module.Factory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Factory entity
 */
@Repository
public interface FactoryRepository extends JpaRepository<Factory, Long> {

    /**
     * Find factories by tenant and active status
     *
     * @param tenant The tenant
     * @param active The active status
     * @return List of factories
     */
    List<Factory> findByTenantAndActive(Tenant tenant, boolean active);

    /**
     * Find factory by ID and active status
     *
     * @param id The factory ID
     * @param active The active status
     * @return Optional factory
     */
    Optional<Factory> findByIdAndActive(Long id, boolean active);

    /**
     * Find factories by name and active status
     *
     * @param name The factory name
     * @param active The active status
     * @return Optional factory
     */
    Optional<Factory> findByNameAndActive(String name, boolean active);

    /**
     * Find factories by type and active status
     *
     * @param type The factory type
     * @param active The active status
     * @return List of factories
     */
    List<Factory> findByTypeAndActive(FactoryType type, boolean active);

    /**
     * Find factories by tenant, type and active status
     *
     * @param tenant The tenant
     * @param type The factory type
     * @param active The active status
     * @return List of factories
     */
    List<Factory> findByTenantAndTypeAndActive(Tenant tenant, FactoryType type, boolean active);

    /**
     * Find all active factories for a tenant
     *
     * @param tenantId The tenant ID
     * @return List of factories
     */
    @Query("SELECT f FROM Factory f WHERE f.tenant.id = :tenantId AND f.active = true")
    List<Factory> findAllActiveByTenantId(Long tenantId);
}