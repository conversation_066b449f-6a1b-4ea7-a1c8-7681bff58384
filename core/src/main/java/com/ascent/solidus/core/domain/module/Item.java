package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "item"
)
public class Item {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "manufacture_id"
  )
  private Manufacture manufacture;

  @ManyToOne
  @JoinColumn(
      name = "manufacturingPart_id"
  )
  private ManufacturingPart manufacturingPart;

  @ManyToOne
  @JoinColumn(
      name = "partCategory_id"
  )
  private PartCategory partCategory;

  @ManyToOne
  @JoinColumn(
      name = "productRating_id"
  )
  private ProductRating productRating;

  @ManyToOne
  @JoinColumn(
      name = "commodity_id"
  )
  private Commodity commodity;

  @ManyToOne
  @JoinColumn(
      name = "productLine_id"
  )
  private ProductLine productLine;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public Manufacture getManufacture() {
    return this.manufacture;
  }

  public void setManufacture(Manufacture manufacture) {
    this.manufacture = manufacture;
  }

  public ManufacturingPart getManufacturingPart() {
    return this.manufacturingPart;
  }

  public void setManufacturingPart(ManufacturingPart manufacturingPart) {
    this.manufacturingPart = manufacturingPart;
  }

  public PartCategory getPartCategory() {
    return this.partCategory;
  }

  public void setPartCategory(PartCategory partCategory) {
    this.partCategory = partCategory;
  }

  public ProductRating getProductRating() {
    return this.productRating;
  }

  public void setProductRating(ProductRating productRating) {
    this.productRating = productRating;
  }

  public Commodity getCommodity() {
    return this.commodity;
  }

  public void setCommodity(Commodity commodity) {
    this.commodity = commodity;
  }

  public ProductLine getProductLine() {
    return this.productLine;
  }

  public void setProductLine(ProductLine productLine) {
    this.productLine = productLine;
  }
}
