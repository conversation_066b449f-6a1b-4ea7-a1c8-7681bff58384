package com.ascent.solidus.core.domain.module;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.lang.String;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(
    name = "checklist"
)
public class CheckList {
  @Id
  @GeneratedValue(
      generator = "uuid"
  )
  @GenericGenerator(
      name = "uuid",
      strategy = "uuid2"
  )
  private String id;

  @ManyToOne
  @JoinColumn(
      name = "parent_module_id",
      referencedColumnName = "moduleId"
  )
  private ParentModule parentModule;

  @ManyToOne
  @JoinColumn(
      name = "spotBuySubCategory_id"
  )
  private SpotBuySubCategory spotBuySubCategory;

  @ManyToOne
  @JoinColumn(
      name = "assembly_id"
  )
  private Assembly assembly;

  @ManyToOne
  @JoinColumn(
      name = "stage_id"
  )
  private Stage stage;

  @ManyToOne
  @JoinColumn(
      name = "auditArea_id"
  )
  private AuditArea auditArea;

  @ManyToOne
  @JoinColumn(
      name = "product_id"
  )
  private Product product;

  public String getId() {
    return this.id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ParentModule getParentModule() {
    return this.parentModule;
  }

  public void setParentModule(ParentModule parentModule) {
    this.parentModule = parentModule;
  }

  public SpotBuySubCategory getSpotBuySubCategory() {
    return this.spotBuySubCategory;
  }

  public void setSpotBuySubCategory(SpotBuySubCategory spotBuySubCategory) {
    this.spotBuySubCategory = spotBuySubCategory;
  }

  public Assembly getAssembly() {
    return this.assembly;
  }

  public void setAssembly(Assembly assembly) {
    this.assembly = assembly;
  }

  public Stage getStage() {
    return this.stage;
  }

  public void setStage(Stage stage) {
    this.stage = stage;
  }

  public AuditArea getAuditArea() {
    return this.auditArea;
  }

  public void setAuditArea(AuditArea auditArea) {
    this.auditArea = auditArea;
  }

  public Product getProduct() {
    return this.product;
  }

  public void setProduct(Product product) {
    this.product = product;
  }
}
