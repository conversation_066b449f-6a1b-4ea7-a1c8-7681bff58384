package com.ascent.solidus.core.domain.module;

public class FilterConfig {
    private String field;
    private String operator;  // "eq", "ne", "gt", "gte", "lt", "lte", "like", etc.
    private Object value;

    // Constructors
    public FilterConfig() {}

    public FilterConfig(String field, String operator, Object value) {
        this.field = field;
        this.operator = operator;
        this.value = value;
    }

    // Getters and Setters
    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "FilterConfig{" +
                "field='" + field + '\'' +
                ", operator='" + operator + '\'' +
                ", value=" + value +
                '}';
    }
}
