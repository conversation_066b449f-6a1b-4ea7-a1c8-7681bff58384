{"moduleId": "123", "moduleName": "CTQ", "moduleType": "parent", "modules": [{"moduleId": "AssignSpotBuy_001", "moduleName": "Assign<PERSON><PERSON><PERSON><PERSON>", "moduleType": "dependent", "dependency": true, "dependencies": ["SpotBuySubCategory", "AppUser", "SpotBuySubCategory"], "parentModuleId": "123", "fields": [{"fieldName": "spotBuySubCategory", "fieldType": "reference", "referenceModule": "SpotBuySubCategory", "required": false}, {"fieldName": "inspectors", "fieldType": "reference_list", "referenceModule": "AppUser", "required": false}, {"fieldName": "categories", "fieldType": "reference_list", "referenceModule": "SpotBuySubCategory", "required": false}]}, {"moduleId": "AssignSpotBuyInspectorr_001", "moduleName": "AssignSpotBuyInspector", "moduleType": "dependent", "dependency": true, "dependencies": ["Assign<PERSON><PERSON><PERSON><PERSON>", "AppUser"], "parentModuleId": "123", "fields": [{"fieldName": "assignSpotBuy", "fieldType": "reference", "referenceModule": "Assign<PERSON><PERSON><PERSON><PERSON>", "required": false}, {"fieldName": "inspector", "fieldType": "reference_list", "referenceModule": "AppUser", "required": false}]}, {"moduleId": "Part_001", "moduleName": "Part", "moduleType": "dependent", "dependency": true, "dependencies": ["PartCategory", "Commodity"], "parentModuleId": "123", "fields": [{"fieldName": "number", "fieldType": "text", "required": true}, {"fieldName": "description", "fieldType": "text", "required": true}, {"fieldName": "SpotBuyType", "fieldType": "enum", "enumValues": ["SPOT_BUY", "IQC", "PDI", "IQC_FIN"], "required": false}, {"fieldName": "commodity", "fieldType": "reference", "referenceModule": "Commodity", "required": false}, {"fieldName": "partCategory", "fieldType": "reference_list", "referenceModule": "PartCategory", "required": false}]}, {"moduleId": "SpotBuySummary_001", "moduleName": "SpotBuySummary", "moduleType": "independent", "dependency": false, "dependencies": [], "parentModuleId": "123", "fields": [{"fieldName": "accepted", "fieldType": "long", "required": true}, {"fieldName": "rejected", "fieldType": "long", "required": true}, {"fieldName": "AqlSamplingSize", "fieldType": "enum", "enumValues": ["NORMAL", "TIGHTEN15", "TIGHTEN25", "ENTIRE", "S1", "NA"], "required": false}]}]}