# Tenant-Feature-Role Management System

## Overview

This document describes how to manage the multi-tenant system where different features like Product Management, CTQ, Audits, etc., are assigned to tenants, and different roles (supervisor, inspector, etc.) have specific permissions for these features.

## System Architecture

### Core Components

1. **Tenants** - Different customers/organizations using the system
2. **Features** - High-level business capabilities (Product Management, CTQ, Audits, etc.)
3. **Modules** - Specific entities within features (ProductFamily, Revision, Part, etc.)
4. **Roles** - User roles with different permission levels
5. **Permissions** - Actions that can be performed (READ, WRITE, DELETE, EXECUTE, ADMIN)

### Configuration Files

All configurations are stored as JSON files in `services/src/main/resources/json/`:

#### 1. tenant-features.json
Defines:
- Available features and their descriptions
- Which tenants have access to which features
- Feature expiry dates (for time-limited access)

#### 2. role-feature-permissions.json
Defines:
- What permissions each role has for each feature
- Which modules within a feature each role can access

#### 3. tenant-module-configs.json
Defines:
- Tenant-specific module customizations
- Custom fields for modules per tenant
- Module enablement per tenant

## Available Roles

```java
public enum RoleName {
    ROLE_SUPER_ADMIN,        // Full system access
    ROLE_ORGANIZATION_ADMIN, // Organization-wide admin
    ROLE_FACTORY_ADMIN,      // Factory-level admin
    ROLE_USER,               // Regular user
    ROLE_SUPERVISOR,         // Supervisor with elevated permissions
    ROLE_SUPPLIER            // External supplier access
}
```

## Available Permission Actions

```java
public enum PermissionAction {
    READ,       // Can view/read data
    WRITE,      // Can create/update data
    DELETE,     // Can delete data
    EXECUTE,    // Can execute operations/actions
    ADMIN       // Full administrative access
}
```

## How to Add a New Tenant

1. **Add tenant to tenant-features.json:**
```json
"tenant_4": {
  "tenant_id": 4,
  "tenant_name": "New Company Inc",
  "features": {
    "PRODUCT_MANAGEMENT": {
      "enabled": true,
      "expiry_date": null
    },
    "CTQ": {
      "enabled": false,
      "expiry_date": null
    }
  }
}
```

2. **Add tenant-specific configurations to tenant-module-configs.json:**
```json
"tenant_4": {
  "tenant_id": 4,
  "tenant_name": "New Company Inc",
  "feature_module_mappings": {
    "PRODUCT_MANAGEMENT": {
      "config_file": "product-management-config.json",
      "custom_modules": {
        "ProductFamily": {
          "enabled": true,
          "custom_fields": []
        }
      }
    }
  }
}
```

## How to Add a New Feature

1. **Add feature definition to tenant-features.json:**
```json
"NEW_FEATURE": {
  "feature_name": "New Feature Name",
  "description": "Description of the new feature",
  "modules": ["Module1", "Module2", "Module3"]
}
```

2. **Add role permissions to role-feature-permissions.json:**
```json
"ROLE_SUPERVISOR": {
  "accessible_features": {
    "NEW_FEATURE": {
      "permissions": ["READ", "WRITE"],
      "modules": ["Module1", "Module2"]
    }
  }
}
```

## How to Modify Role Permissions

Edit the `role-feature-permissions.json` file to change what each role can access:

```json
"ROLE_SUPERVISOR": {
  "accessible_features": {
    "PRODUCT_MANAGEMENT": {
      "permissions": ["READ", "WRITE", "EXECUTE"],  // Added EXECUTE
      "modules": ["ProductFamily", "Revision"]      // Removed other modules
    }
  }
}
```

## Dynamic Module Generation

The system uses your existing `module-config.json` files to generate entities, services, repositories, and controllers. The tenant-feature-role system works on top of this by:

1. **Feature-Level Access Control**: Checking if a tenant has access to a feature
2. **Role-Based Permissions**: Checking if a user's role has specific permissions
3. **Module-Level Filtering**: Showing only modules the user can access
4. **Action-Level Security**: Controlling what actions users can perform

## Integration Points

### 1. FeatureConfigurationService
Loads all JSON configurations into the database on startup.

### 2. FeatureAccessControlService
Provides runtime access control checks:
- `hasUserAccessToFeature(userId, featureId)`
- `hasUserPermissionForFeature(userId, featureId, permission)`
- `canUserAccessModule(userId, featureId, moduleName)`

### 3. @RequireFeatureAccess Annotation
Use on controllers/methods to enforce access control:
```java
@RequireFeatureAccess(featureId = "PRODUCT_MANAGEMENT", permission = PermissionAction.WRITE)
public ResponseEntity<?> createProduct() {
    // Method implementation
}
```

## Best Practices

1. **Feature Naming**: Use UPPER_CASE with underscores for feature IDs
2. **Module Naming**: Use PascalCase for module names to match your existing convention
3. **Permissions**: Start with minimal permissions and add more as needed
4. **Expiry Dates**: Use ISO 8601 format (yyyy-MM-dd'T'HH:mm:ss'Z')
5. **Testing**: Always test configuration changes in a development environment first

## Configuration Reload

To reload configurations without restarting the application, call:
```java
featureConfigurationService.initializeConfigurations();
```

This will re-read all JSON files and update the database accordingly.

## API Endpoints

The system provides REST endpoints for managing tenant-feature-role configurations:

### Get User's Accessible Features
```
GET /api/tenant-management/user/{userId}/features
```

### Get User's Feature Permissions
```
GET /api/tenant-management/user/{userId}/features/{featureId}/permissions
```

### Get Tenant Feature Summary
```
GET /api/tenant-management/tenant/{tenantId}/summary
```

### Get Filtered Module Configuration
```
GET /api/tenant-management/user/{userId}/tenant/{tenantId}/feature/{featureId}/config
```

## Example Usage Scenarios

### Scenario 1: Inspector Role Access
An inspector (ROLE_SUPERVISOR) at tenant "Acme Manufacturing" needs to:
1. Access CTQ feature ✓ (tenant has CTQ enabled)
2. Create new Part records ✓ (role has WRITE permission)
3. Delete SpotBuySummary records ✗ (role doesn't have DELETE permission)

### Scenario 2: Supplier Access
A supplier (ROLE_SUPPLIER) needs to:
1. Update their supplier profile ✓ (has WRITE on SupplierProfile module)
2. View inventory items ✓ (has READ on StockItem module)
3. Create new products ✗ (no access to PRODUCT_MANAGEMENT feature)

### Scenario 3: Tenant-Specific Customization
"Global Tech Solutions" has:
1. Custom "technology_type" field in ProductFamily module
2. Supplier certification levels (BASIC, PREFERRED, STRATEGIC)
3. Different module configurations than other tenants
