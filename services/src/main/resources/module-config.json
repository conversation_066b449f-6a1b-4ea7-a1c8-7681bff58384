{"moduleId": "123", "moduleName": "Product_Management", "moduleType": "parent", "modules": [{"moduleId": "ProductFamily_001", "moduleName": "ProductFamily", "moduleType": "independent", "parentModuleId": "123", "fields": [{"fieldName": "productfamily", "fieldType": "text", "required": true}, {"fieldName": "model_number", "fieldType": "number", "required": true}]}, {"moduleId": "revision_001", "moduleName": "Revision", "moduleType": "dependent", "dependency": true, "dependencies": ["ProductFamily"], "parentModuleId": "123", "fields": [{"fieldName": "revision_number", "fieldType": "number", "required": true}, {"fieldName": "productfamily", "fieldType": "reference", "referenceModule": "ProductFamily", "required": true}]}, {"moduleId": "assembly_001", "moduleName": "Assembly", "moduleType": "dependent", "dependency": true, "dependencies": ["ProductFamily"], "parentModuleId": "123", "fields": [{"fieldName": "assembly_name", "fieldType": "text", "required": true}, {"fieldName": "productfamily", "fieldType": "reference", "referenceModule": "ProductFamily", "required": true}]}, {"moduleId": "stage_001", "moduleName": "Stage", "moduleType": "dependent", "dependency": true, "dependencies": ["ProductFamily", "Assembly"], "parentModuleId": "123", "fields": [{"fieldName": "stage_name", "fieldType": "text", "required": true}, {"fieldName": "productfamily", "fieldType": "reference", "referenceModule": "ProductFamily", "required": true}, {"fieldName": "assembly_name", "fieldType": "reference", "referenceModule": "Assembly", "required": true}]}, {"moduleId": "questions_001", "moduleName": "Questions", "moduleType": "dependent", "dependency": true, "dependencies": ["Assembly", "Stage"], "parentModuleId": "123", "fields": [{"fieldName": "question_text", "fieldType": "text", "required": true}, {"fieldName": "choice", "fieldType": "boolean", "required": true}, {"fieldName": "assembly", "fieldType": "reference", "referenceModule": "Assembly", "required": true}, {"fieldName": "stage", "fieldType": "reference", "referenceModule": "Stage", "required": true}, {"fieldName": "expected_value", "fieldType": "text", "required": false, "choice": false}, {"fieldName": "tolerance_limit_positive", "fieldType": "number", "required": false, "choice": false}, {"fieldName": "tolerance_limit_negative", "fieldType": "number", "required": false, "choice": false}, {"fieldName": "units", "fieldType": "text", "required": false, "choice": false}]}]}