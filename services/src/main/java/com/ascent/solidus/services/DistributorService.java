package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DistributorRepository;
import com.ascent.solidus.core.domain.module.Distributor;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DistributorService {
  private final DistributorRepository repository;

  public DistributorService(DistributorRepository repository) {
    this.repository = repository;
  }

  public Page<Distributor> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Distributor> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Distributor findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Distributor save(Distributor entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
