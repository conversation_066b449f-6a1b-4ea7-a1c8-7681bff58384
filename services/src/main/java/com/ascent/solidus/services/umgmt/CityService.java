package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.umgmt.City;
import com.ascent.solidus.core.domain.umgmt.State;
import com.ascent.solidus.core.repository.umgmt.CityRepository;
import com.ascent.solidus.services.tenant.TenantService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CityService {

    @Autowired
    CityRepository cityRepository;

    @Autowired
    StateService stateService;

    @Autowired
    TenantService tenantService;

    @Transactional(readOnly = true)
    public List<City> findAll() {
        return cityRepository.findAll();
    }

    @Transactional(readOnly = true)
    public City findById(Long id) {
        return cityRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("City not found with id: " + id));
    }

    @Transactional(readOnly = true)
    public List<City> findByState(Long stateId) {
        State state = stateService.findById(stateId);
        return cityRepository.findByState(state);
    }

    @Transactional
    public City save(City city) {
        return cityRepository.save(city);
    }

    @Transactional
    public City createCity(String name, Long stateId) {
        City city = new City();
        city.setName(name);

        if (stateId != null) {
            State state = stateService.findById(stateId);
            city.setState(state);
            city.setActive(true);
            city.setTenant(tenantService.getCurrentTenant());
        }

        return cityRepository.save(city);
    }

    @Transactional
    public City update(City city) {
        // Ensure the city exists
        findById(city.getId());
        return cityRepository.save(city);
    }

    @Transactional
    public City updateCity(Long id, String name, Long stateId) {
        City city = findById(id);
        city.setName(name);

        if (stateId != null) {
            State state = stateService.findById(stateId);
            city.setState(state);
        }

        return cityRepository.save(city);
    }

    @Transactional
    public void delete(Long id) {
        // Ensure the city exists
        findById(id);
        cityRepository.deleteById(id);
    }
}