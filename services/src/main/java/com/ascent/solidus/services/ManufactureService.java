package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ManufactureRepository;
import com.ascent.solidus.core.domain.module.Manufacture;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ManufactureService {
  private final ManufactureRepository repository;

  public ManufactureService(ManufactureRepository repository) {
    this.repository = repository;
  }

  public Page<Manufacture> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Manufacture> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Manufacture findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Manufacture save(Manufacture entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
