package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationCCMemberRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationCCMember;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationCCMemberService {
  private final FaultIntimationCCMemberRepository repository;

  public FaultIntimationCCMemberService(FaultIntimationCCMemberRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationCCMember> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationCCMember> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationCCMember findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationCCMember save(FaultIntimationCCMember entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
