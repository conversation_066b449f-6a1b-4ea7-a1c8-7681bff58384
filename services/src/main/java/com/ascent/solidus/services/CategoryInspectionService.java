package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CategoryInspectionRepository;
import com.ascent.solidus.core.domain.module.CategoryInspection;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CategoryInspectionService {
  private final CategoryInspectionRepository repository;

  public CategoryInspectionService(CategoryInspectionRepository repository) {
    this.repository = repository;
  }

  public Page<CategoryInspection> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<CategoryInspection> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public CategoryInspection findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public CategoryInspection save(CategoryInspection entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
