package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ParameterSampleRepository;
import com.ascent.solidus.core.domain.module.ParameterSample;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ParameterSampleService {
  private final ParameterSampleRepository repository;

  public ParameterSampleService(ParameterSampleRepository repository) {
    this.repository = repository;
  }

  public Page<ParameterSample> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ParameterSample> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ParameterSample findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ParameterSample save(ParameterSample entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
