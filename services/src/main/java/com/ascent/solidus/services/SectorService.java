package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SectorRepository;
import com.ascent.solidus.core.domain.module.Sector;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SectorService {
  private final SectorRepository repository;

  public SectorService(SectorRepository repository) {
    this.repository = repository;
  }

  public Page<Sector> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Sector> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Sector findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Sector save(Sector entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
