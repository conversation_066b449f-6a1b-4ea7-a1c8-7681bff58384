package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SamplingPlanLimitRepository;
import com.ascent.solidus.core.domain.module.SamplingPlanLimit;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SamplingPlanLimitService {
  private final SamplingPlanLimitRepository repository;

  public SamplingPlanLimitService(SamplingPlanLimitRepository repository) {
    this.repository = repository;
  }

  public Page<SamplingPlanLimit> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<SamplingPlanLimit> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public SamplingPlanLimit findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public SamplingPlanLimit save(SamplingPlanLimit entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
