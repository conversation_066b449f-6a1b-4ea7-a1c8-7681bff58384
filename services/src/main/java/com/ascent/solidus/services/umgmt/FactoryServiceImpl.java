package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantMapping;
import com.ascent.solidus.core.domain.tenant.TenantType;
import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.module.Factory;
import com.ascent.solidus.core.repository.tenant.TenantMappingRepository;
import com.ascent.solidus.core.repository.tenant.TenantRepository;
import com.ascent.solidus.core.repository.umgmt.FactoryRepository;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.services.tenant.TenantService;

import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

@Service

public class FactoryServiceImpl implements FactoryService {


    @Autowired
    FactoryRepository factoryRepository;

    @Autowired
    AppUserRepository appUserRepository;

    @Autowired
    TenantService tenantService;

    @Autowired
    TenantRepository tenantRepository;

    @Autowired
    TenantMappingRepository tenantMappingRepository;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_SUPERVISOR', 'ROLE_INSPECTOR')")
    public ListResponse findAll(SearchCriteria searchCriteria) {
//        log.debug("Finding all factories with criteria: {}", searchCriteria);
        Assert.notNull(searchCriteria, "'searchCriteria' is required.");

        ListResponse response = new ListResponse();
        List<Factory> factories = factoryRepository.findAll();
        response.setData(factories);
        response.setRecordsTotal((long) factories.size());
        response.setRecordsFiltered((long) factories.size());

        return response;
    }

    @Override
    @Transactional
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN')")
    public Factory save(Factory factory) {
//        log.debug("Creating new factory: {}", factory.getName());
        Assert.notNull(factory, "'factory' is required.");

        // Check if factory with same name already exists
        factoryRepository.findByNameAndActive(factory.getName(), true)
            .ifPresent(existing -> {
//                log.warn("Factory with name already exists: {}", factory.getName());
                throw new IllegalArgumentException("Factory with this name already exists");
            });

        // Create a new tenant for this factory with ROLE_ADMIN type
        Tenant newTenant = new Tenant();
        newTenant.setTenantType(TenantType.ROLE_FACTORY_ADMIN);
        newTenant.setActive(true);
        newTenant.setCreatedOn(new Date());

        // Save the new tenant
        Tenant savedTenant = tenantRepository.save(newTenant);

        // Create tenant mapping with current tenant as parent
        Tenant currentTenant = tenantService.getCurrentTenant();
        TenantMapping tenantMapping = new TenantMapping();
        tenantMapping.setParent(currentTenant);
        tenantMapping.setChild(savedTenant);
        tenantMapping.setActive(true);
        tenantMapping.setCreatedOn(new Date());
        tenantMappingRepository.save(tenantMapping);

        // Set factory properties
        factory.setActive(true);
        factory.setCreatedOn(new Date());
        factory.setTenant(savedTenant);

        // Save factory
        Factory saved = factoryRepository.save(factory);
//        log.info("Created factory with ID: {}", saved.getId());

        return saved;
    }

    @Override
    @Transactional
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN')")
    public Factory update(Factory factory) {
//        log.debug("Updating factory with ID: {}", factory.getId());
        Assert.notNull(factory, "'factory' is required.");
        Assert.notNull(factory.getId(), "'factory.id' is required.");

        // Get existing factory
        Factory existing = factoryRepository.findById(factory.getId())
                .orElseThrow(() -> {
//                    log.error("Factory not found with ID: {}", factory.getId());
                    return new EntityNotFoundException("Factory not found");
                });

        // Check if another factory with the same name exists
        factoryRepository.findByNameAndActive(factory.getName(), true)
            .ifPresent(found -> {
                if (!found.getId().equals(factory.getId())) {
//                    log.warn("Another factory with name already exists: {}", factory.getName());
                    throw new IllegalArgumentException("Another factory with this name already exists");
                }
            });

        // Update factory properties
        existing.setName(factory.getName());
        existing.setEmail(factory.getEmail());
        existing.setMobile(factory.getMobile());
        existing.setAddressLine1(factory.getAddressLine1());
        existing.setAddressLine2(factory.getAddressLine2());
        existing.setArea(factory.getArea());
        existing.setCity(factory.getCity());
        existing.setContactPersonName(factory.getContactPersonName());
        existing.setCountry(factory.getCountry());
        existing.setZipCode(factory.getZipCode());
        existing.setOtherContactNumber(factory.getOtherContactNumber());
        existing.setState(factory.getState());
        existing.setUpdatedOn(new Date());

        // Save updated factory
        Factory updated = factoryRepository.save(existing);
//        log.info("Updated factory with ID: {}", updated.getId());

        return updated;
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public Factory getOne(Long id) {
//        log.debug("Getting factory with ID: {}", id);
        Assert.notNull(id, "'id' is required.");

        // Get existing factory
        return factoryRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("Factory not found with ID: {}", id);
                    return new EntityNotFoundException("Factory not found");
                });
    }

    @Override
    @Transactional
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN')")
    public void delete(Long id) {
//        log.debug("Deleting factory with ID: {}", id);
        Assert.notNull(id, "'id' is required.");

        // Get existing factory
        Factory existing = factoryRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("Factory not found with ID: {}", id);
                    return new EntityNotFoundException("Factory not found");
                });

        // Check if factory has associated users
        List<AppUser> users = appUserRepository.findByFactory(existing);
        if (!users.isEmpty()) {
//            log.warn("Factory has associated users, cannot delete: {}", id);
            throw new IllegalStateException("Factory has associated users and cannot be deleted");
        }

        // Soft delete by setting active to false
        existing.setActive(false);
        existing.setUpdatedOn(new Date());

        // Save updated factory
        factoryRepository.save(existing);
//        log.info("Deleted (deactivated) factory with ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUPERVISOR', 'ROLE_INSPECTOR')")
    public Factory getFactoryByUser(AppUser loggedInUser) {
//        log.debug("Getting factory for user with ID: {}", loggedInUser.getId());
        Assert.notNull(loggedInUser, "'loggedInUser' is required.");

        // Get user with factory
        AppUser user = appUserRepository.findById(loggedInUser.getId())
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", loggedInUser.getId());
                    return new EntityNotFoundException("User not found");
                });

        // Check if user has a factory
        if (user.getFactory() == null) {
//            log.error("User has no associated factory: {}", loggedInUser.getId());
            throw new EntityNotFoundException("User has no associated factory");
        }

        return user.getFactory();
    }
}