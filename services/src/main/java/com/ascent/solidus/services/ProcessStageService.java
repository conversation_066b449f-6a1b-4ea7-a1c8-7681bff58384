package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProcessStageRepository;
import com.ascent.solidus.core.domain.module.ProcessStage;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProcessStageService {
  private final ProcessStageRepository repository;

  public ProcessStageService(ProcessStageRepository repository) {
    this.repository = repository;
  }

  public Page<ProcessStage> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProcessStage> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProcessStage findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProcessStage save(ProcessStage entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
