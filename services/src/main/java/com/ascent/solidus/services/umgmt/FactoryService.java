package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.module.Factory;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;

/**
 * Service interface for managing factories
 */
public interface FactoryService {

    /**
     * Find all factories with pagination and filtering
     *a
     * @param searchCriteria The search criteria for pagination and filtering
     * @return A ListResponse containing the factories
     */
    ListResponse findAll(SearchCriteria searchCriteria);

    /**
     * Save a new factory
     *
     * @param factory The factory to save
     * @return The saved factory
     */
    Factory save(Factory factory);

    /**
     * Update an existing factory
     *
     * @param factory The factory to update
     * @return The updated factory
     */
    Factory update(Factory factory);

    /**
     * Get a factory by ID
     *
     * @param id The ID of the factory
     * @return The factory
     */
    Factory getOne(Long id);

    /**
     * Delete a factory by ID
     *
     * @param id The ID of the factory to delete
     */
    void delete(Long id);

    /**
     * Get factory by user
     *
     * @param loggedInUser The logged in user
     * @return The factory associated with the user
     */
    Factory getFactoryByUser(AppUser loggedInUser);
}