package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.module.Factory;
import com.ascent.solidus.core.domain.umgmt.Role;
import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import com.ascent.solidus.core.repository.umgmt.FactoryRepository;
import com.ascent.solidus.core.repository.umgmt.RoleRepository;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import jakarta.persistence.EntityNotFoundException;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

/**
 * Implementation of the FactoryAdminService interface.
 * Provides functionality for managing factory administrators.
 */
@Service
public class FactoryAdminServiceImpl implements FactoryAdminService {

    @Autowired
    AppUserRepository appUserRepository;

    @Autowired
    FactoryRepository factoryRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('ROLE_ORGANIZATION_ADMIN') or hasRole('ROLE_SUPER_ADMIN')")
    public ListResponse findAll(SearchCriteria searchCriteria, AppUser loggedInUser) {
//        log.debug("Finding all factory admins with criteria: {}", searchCriteria);
        Assert.notNull(searchCriteria, "'searchCriteria' is required.");
        Assert.notNull(loggedInUser, "'loggedInUser' is required.");
        
        // Implement a simple search if the repository method is not available
        // This is a placeholder - you'll need to implement the actual search logic
        ListResponse response = new ListResponse();
        List<AppUser> users = appUserRepository.findAll();
        response.setData(users);
        response.setRecordsTotal((long) users.size());
        response.setRecordsFiltered((long) users.size());
        
        return response;
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ROLE_ORGANIZATION_ADMIN') or hasRole('ROLE_SUPER_ADMIN')")
    public AppUser save(AppUser appUser) {
//        log.debug("Creating new factory admin: {}", appUser.getEmail());
        Assert.notNull(appUser, "'appUser' is required.");
        Assert.notNull(appUser.getFactory(), "'appUser.factory' is required.");
        Assert.notNull(appUser.getFactory().getId(), "'appUser.factory.id' is required.");
        
        // Check if email already exists
        Optional<AppUser> existingByEmail = appUserRepository.findByEmailAndActive(appUser.getEmail(), true);
        if (existingByEmail.isPresent()) {
//            log.warn("Email already exists: {}", appUser.getEmail());
            throw new IllegalArgumentException("User with this email already exists");
        }
        
        // Check if username already exists if provided
        if (appUser.getUsername() != null && !appUser.getUsername().isEmpty()) {
            Optional<AppUser> existingByUsername = appUserRepository.findByUsernameAndActive(appUser.getUsername(), true);
            if (existingByUsername.isPresent()) {
//                log.warn("Username already exists: {}", appUser.getUsername());
                throw new IllegalArgumentException("User with this username already exists");
            }
        } else {
            // Set email as username if not provided
            appUser.setUsername(appUser.getEmail());
        }
        
        // Get factory
        Factory factory = factoryRepository.findById(appUser.getFactory().getId())
                .orElseThrow(() -> {
//                    log.error("Factory not found with ID: {}", appUser.getFactory().getId());
                    return new EntityNotFoundException("Factory not found");
                });
        
        // Set user properties
        appUser.setCreatedOn(new Date());
        appUser.setActive(true);
        appUser.setEnabled(true);
        
        // Generate and set temporary password
        String tempPassword = RandomStringUtils.randomAlphanumeric(10);
        appUser.setPassword(passwordEncoder.encode(tempPassword));
        
        // Set factory and tenant
        appUser.setFactory(factory);
        appUser.setTenant(factory.getTenant());
        
        // Assign factory admin role
        Role adminRole = roleRepository.findByName(RoleName.ROLE_FACTORY_ADMIN)
                .orElseThrow(() -> new EntityNotFoundException("Factory admin role not found"));
        
        // Initialize assignedRoles if null
        if (appUser.getAssignedRoles() == null) {
            appUser.setAssignedRoles(new HashSet<>());
        }

        RoleAssignment roleAssignment = new RoleAssignment();
        roleAssignment.setAppUser(appUser);
        roleAssignment.setRole(adminRole);
        roleAssignment.setTenant(factory.getTenant());
        roleAssignment.setActive(true);
        roleAssignment.setCreatedOn(new Date());
        
        appUser.getAssignedRoles().add(roleAssignment);
        
        // Save user
        AppUser saved = appUserRepository.save(appUser);
//        log.info("Created factory admin with ID: {}", saved.getId());
        
        // Email sending would go here, but we'll skip it for now
//        log.info("Would send registration email to: {} with temp password: {}", appUser.getEmail(), tempPassword);
        
        return saved;
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ROLE_ORGANIZATION_ADMIN') or hasRole('ROLE_SUPER_ADMIN')")
    public AppUser update(AppUser appUser) {
//        log.debug("Updating factory admin with ID: {}", appUser.getId());
        Assert.notNull(appUser, "'appUser' is required.");
        Assert.notNull(appUser.getId(), "'appUser.id' is required.");
        
        // Get existing user
        AppUser existing = appUserRepository.findById(appUser.getId())
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", appUser.getId());
                    return new EntityNotFoundException("User not found");
                });
        
        // Check if email already exists for another user
        Optional<AppUser> existingByEmail = appUserRepository.findByEmailAndActive(appUser.getEmail(), true);
        if (existingByEmail.isPresent() && !existingByEmail.get().getId().equals(appUser.getId())) {
//            log.warn("Email already exists for another user: {}", appUser.getEmail());
            throw new IllegalArgumentException("Email already exists for another user");
        }
        
        // Check if username already exists for another user
        if (appUser.getUsername() != null && !appUser.getUsername().isEmpty()) {
            Optional<AppUser> existingByUsername = appUserRepository.findByUsernameAndActive(appUser.getUsername(), true);
            if (existingByUsername.isPresent() && !existingByUsername.get().getId().equals(appUser.getId())) {
//                log.warn("Username already exists for another user: {}", appUser.getUsername());
                throw new IllegalArgumentException("Username already exists for another user");
            }
            existing.setUsername(appUser.getUsername());
        }
        
        // Update user properties
        existing.setFirstName(appUser.getFirstName());
        existing.setLastName(appUser.getLastName());
        existing.setEmail(appUser.getEmail());
        existing.setPhone(appUser.getPhone());
        
        // Save updated user
        AppUser updated = appUserRepository.save(existing);
//        log.info("Updated factory admin with ID: {}", updated.getId());
        
        return updated;
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('ROLE_ORGANIZATION_ADMIN') or hasRole('ROLE_SUPER_ADMIN')")
    public AppUser getOne(Long id) {
//        log.debug("Getting factory admin with ID: {}", id);
        Assert.notNull(id, "'id' is required.");
        
        // Get existing user
        AppUser existing = appUserRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", id);
                    return new EntityNotFoundException("User not found");
                });
        
        // Get roles
        List<Role> roles = new ArrayList<>();
        if (existing.getAssignedRoles() != null && !existing.getAssignedRoles().isEmpty()) {
            for (RoleAssignment roleAssignment : existing.getAssignedRoles()) {
                roles.add(roleAssignment.getRole());
            }
        }

        // Set the roles list on the user
        existing.setRoles(roles);
        
        return existing;
    }
    
    @Override
    @Transactional
    @PreAuthorize("hasRole('ROLE_ORGANIZATION_ADMIN') or hasRole('ROLE_SUPER_ADMIN')")
    public AppUser resendMail(Long id) {
//        log.debug("Resending credentials email to factory admin with ID: {}", id);
        Assert.notNull(id, "'id' is required.");
        
        // Get existing user
        AppUser existing = appUserRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", id);
                    return new EntityNotFoundException("User not found");
                });
        
        // Generate new temporary password
        String tempPassword = RandomStringUtils.randomAlphanumeric(10);
        existing.setPassword(passwordEncoder.encode(tempPassword));
        
        // Save user with new password
        AppUser updated = appUserRepository.save(existing);
//        log.info("Updated password for factory admin with ID: {}", updated.getId());
//
//        // Email sending would go here, but we'll skip it for now
//        log.info("Would send registration email to: {} with temp password: {}", existing.getEmail(), tempPassword);
        
        return updated;
    }
}