    package com.ascent.solidus.services.umgmt;

    import com.ascent.solidus.core.domain.tenant.Organization;
    import com.ascent.solidus.core.domain.tenant.Tenant;
    import com.ascent.solidus.core.domain.module.AppUser;
    import com.ascent.solidus.core.domain.umgmt.Role;
    import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
    import com.ascent.solidus.core.constants.RoleName;
    import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
    import com.ascent.solidus.core.repository.umgmt.RoleAssignmentRepository;
    import com.ascent.solidus.core.repository.umgmt.RoleRepository;
    import com.ascent.solidus.services.tenant.OrganizationService;
    import com.ascent.solidus.services.tenant.TenantService;
    import jakarta.persistence.EntityNotFoundException;
    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.security.crypto.password.PasswordEncoder;
    import org.springframework.stereotype.Service;
    import org.springframework.transaction.annotation.Transactional;

    import java.util.List;
    import java.util.Optional;
    import java.util.stream.Collectors;

    @Service

    public class AppUserService {

        @Autowired
        AppUserRepository appUserRepository;

        @Autowired
        RoleRepository roleRepository;

        @Autowired
        RoleAssignmentRepository roleAssignmentRepository;

        @Autowired
        TenantService tenantService;

        @Autowired
        OrganizationService organizationService;

        @Autowired
        PasswordEncoder passwordEncoder;

        @Transactional(readOnly = true)
        public AppUser findById(Long id) {
            return appUserRepository.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("User not found with id: " + id));
        }

        @Transactional(readOnly = true)
        public AppUser findByUsername(String username) {
            return appUserRepository.findByUsernameAndActive(username, true)
                    .orElseThrow(() -> new EntityNotFoundException("User not found with username: " + username));
        }

        @Transactional(readOnly = true)
        public List<AppUser> findByTenant(Long tenantId) {
            Tenant tenant = tenantService.findById(tenantId);
            return appUserRepository.findByTenantAndActive(tenant, true);
        }

        @Transactional(readOnly = true)
        public List<AppUser> findByOrganization(Long organizationId) {
            Organization organization = organizationService.findById(organizationId);
            return appUserRepository.findByOrganizationAndActive(organization, true);
        }

        @Transactional
        public AppUser createUser(AppUser user, Long tenantId, Long organizationId, List<RoleName> roleNames) {
            // Set tenant
            Tenant tenant = tenantService.findById(tenantId);
            user.setTenant(tenant);

            // Set organization if provided
            if (organizationId != null) {
                Organization organization = organizationService.findById(organizationId);
                user.setOrganization(organization);
            }

            // Encode password
            user.setPassword(passwordEncoder.encode(user.getPassword()));

            // Set active status
            user.setActive(true);

            // Save user
            AppUser savedUser = appUserRepository.save(user);

            // Assign roles
            if (roleNames != null && !roleNames.isEmpty()) {
                for (RoleName roleName : roleNames) {
                    assignRole(savedUser.getId(), roleName, tenantId);
                }
            }

            return savedUser;
        }

        @Transactional
        public void assignRole(Long userId, RoleName roleName, Long tenantId) {
            AppUser user = findById(userId);
            Tenant tenant = tenantService.findById(tenantId);

            Role role = roleRepository.findByName(roleName)
                    .orElseThrow(() -> new EntityNotFoundException("Role not found: " + roleName));

            // Check if role assignment already exists
            Optional<RoleAssignment> existingAssignment =
                    roleAssignmentRepository.findByAppUserAndRoleAndTenantAndActive(user, role, tenant, true);

            if (existingAssignment.isEmpty()) {
                RoleAssignment roleAssignment = new RoleAssignment();
                roleAssignment.setAppUser(user);
                roleAssignment.setRole(role);
                roleAssignment.setTenant(tenant);
                roleAssignment.setActive(true);

                roleAssignmentRepository.save(roleAssignment);
            }
        }

        @Transactional
        public void removeRole(Long userId, RoleName roleName, Long tenantId) {
            AppUser user = findById(userId);
            Tenant tenant = tenantService.findById(tenantId);

            Role role = roleRepository.findByName(roleName)
                    .orElseThrow(() -> new EntityNotFoundException("Role not found: " + roleName));

            Optional<RoleAssignment> existingAssignment =
                    roleAssignmentRepository.findByAppUserAndRoleAndTenantAndActive(user, role, tenant, true);

            existingAssignment.ifPresent(assignment -> {
                assignment.setActive(false);
                roleAssignmentRepository.save(assignment);
            });
        }

        @Transactional
        public AppUser updateUser(Long id, AppUser userDetails) {
            AppUser user = findById(id);

            // Update basic information
            user.setFirstName(userDetails.getFirstName());
            user.setLastName(userDetails.getLastName());
            user.setEmail(userDetails.getEmail());

            // Don't update password here - use separate method for that

            return appUserRepository.save(user);
        }

        @Transactional
        public void updatePassword(Long id, String newPassword) {
            AppUser user = findById(id);
            user.setPassword(passwordEncoder.encode(newPassword));
            appUserRepository.save(user);
        }

        @Transactional
        public void deactivateUser(Long id) {
            AppUser user = findById(id);
            user.setActive(false);
            appUserRepository.save(user);
        }

        @Transactional(readOnly = true)
        public List<RoleAssignment> getUserRoles(Long userId) {
            return roleAssignmentRepository.findAllActiveRolesByUserId(userId);
        }

        @Transactional(readOnly = true)
        public List<RoleName> getUserRoleNames(Long userId) {
            return getUserRoles(userId).stream()
                    .map(ra -> ra.getRole().getName())
                    .collect(Collectors.toList());
        }
    }