package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DocNumberRepository;
import com.ascent.solidus.core.domain.module.DocNumber;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DocNumberService {
  private final DocNumberRepository repository;

  public DocNumberService(DocNumberRepository repository) {
    this.repository = repository;
  }

  public Page<DocNumber> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DocNumber> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DocNumber findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DocNumber save(DocNumber entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
