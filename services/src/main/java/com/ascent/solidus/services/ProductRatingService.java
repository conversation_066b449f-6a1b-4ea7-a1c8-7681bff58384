package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProductRatingRepository;
import com.ascent.solidus.core.domain.module.ProductRating;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProductRatingService {
  private final ProductRatingRepository repository;

  public ProductRatingService(ProductRatingRepository repository) {
    this.repository = repository;
  }

  public Page<ProductRating> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProductRating> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProductRating findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProductRating save(ProductRating entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
