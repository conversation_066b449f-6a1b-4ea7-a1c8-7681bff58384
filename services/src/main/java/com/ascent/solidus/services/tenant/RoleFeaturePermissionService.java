package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing role-feature permissions
 * Reads from role-feature-permissions.json to determine what roles can access which features
 */
@Service
public class RoleFeaturePermissionService {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleFeaturePermissionService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Get role-feature permission
     */
    public Optional<RoleFeaturePermission> getRoleFeaturePermission(RoleName roleName, String featureId) {
        try {
            JsonNode rolePermissions = loadRolePermissions();
            
            if (rolePermissions.has(roleName.name())) {
                JsonNode role = rolePermissions.get(roleName.name());
                JsonNode accessibleFeatures = role.get("accessible_features");
                
                if (accessibleFeatures != null && accessibleFeatures.has(featureId)) {
                    JsonNode feature = accessibleFeatures.get(featureId);
                    
                    RoleFeaturePermission permission = new RoleFeaturePermission();
                    permission.setRoleName(roleName);
                    permission.setFeatureId(featureId);
                    
                    // Parse permissions
                    List<PermissionAction> permissions = new ArrayList<>();
                    JsonNode permissionsNode = feature.get("permissions");
                    if (permissionsNode != null && permissionsNode.isArray()) {
                        permissionsNode.forEach(perm -> {
                            try {
                                permissions.add(PermissionAction.valueOf(perm.asText()));
                            } catch (IllegalArgumentException e) {
                                logger.warn("Unknown permission action: {}", perm.asText());
                            }
                        });
                    }
                    permission.setPermissions(permissions);
                    
                    // Parse accessible modules
                    List<String> modules = new ArrayList<>();
                    JsonNode modulesNode = feature.get("modules");
                    if (modulesNode != null && modulesNode.isArray()) {
                        modulesNode.forEach(module -> modules.add(module.asText()));
                    }
                    permission.setAccessibleModules(modules);
                    
                    return Optional.of(permission);
                }
            }
        } catch (Exception e) {
            logger.error("Error getting role feature permission for role {} and feature {}: {}", 
                        roleName, featureId, e.getMessage());
        }
        
        return Optional.empty();
    }
    
    /**
     * Check if role has specific permission for a feature
     */
    public boolean hasRolePermissionForFeature(RoleName roleName, String featureId, PermissionAction permission) {
        Optional<RoleFeaturePermission> rolePermission = getRoleFeaturePermission(roleName, featureId);
        return rolePermission.map(rp -> rp.getPermissions().contains(permission)).orElse(false);
    }
    
    /**
     * Check if role can access a specific module within a feature
     */
    public boolean canRoleAccessModule(RoleName roleName, String featureId, String moduleName) {
        Optional<RoleFeaturePermission> rolePermission = getRoleFeaturePermission(roleName, featureId);
        return rolePermission.map(rp -> rp.getAccessibleModules().contains(moduleName)).orElse(false);
    }
    
    /**
     * Get all permissions for a role and feature
     */
    public List<PermissionAction> getRolePermissionsForFeature(RoleName roleName, String featureId) {
        Optional<RoleFeaturePermission> rolePermission = getRoleFeaturePermission(roleName, featureId);
        return rolePermission.map(RoleFeaturePermission::getPermissions).orElse(new ArrayList<>());
    }
    
    /**
     * Get accessible modules for multiple roles and a feature
     */
    public List<String> getAccessibleModulesForRoles(List<RoleName> roles, String featureId) {
        return roles.stream()
                .flatMap(role -> {
                    Optional<RoleFeaturePermission> permission = getRoleFeaturePermission(role, featureId);
                    return permission.map(p -> p.getAccessibleModules().stream()).orElse(java.util.stream.Stream.empty());
                })
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * Get all features accessible to a role
     */
    public List<String> getAccessibleFeaturesForRole(RoleName roleName) {
        List<String> features = new ArrayList<>();
        
        try {
            JsonNode rolePermissions = loadRolePermissions();
            
            if (rolePermissions.has(roleName.name())) {
                JsonNode role = rolePermissions.get(roleName.name());
                JsonNode accessibleFeatures = role.get("accessible_features");
                
                if (accessibleFeatures != null) {
                    accessibleFeatures.fieldNames().forEachRemaining(features::add);
                }
            }
        } catch (Exception e) {
            logger.error("Error getting accessible features for role {}: {}", roleName, e.getMessage());
        }
        
        return features;
    }
    
    /**
     * Get all accessible modules for a role across all features
     */
    public List<String> getAllAccessibleModulesForRole(RoleName roleName) {
        List<String> modules = new ArrayList<>();
        
        try {
            JsonNode rolePermissions = loadRolePermissions();
            
            if (rolePermissions.has(roleName.name())) {
                JsonNode role = rolePermissions.get(roleName.name());
                JsonNode accessibleFeatures = role.get("accessible_features");
                
                if (accessibleFeatures != null) {
                    accessibleFeatures.fieldNames().forEachRemaining(featureId -> {
                        JsonNode feature = accessibleFeatures.get(featureId);
                        JsonNode modulesNode = feature.get("modules");
                        if (modulesNode != null && modulesNode.isArray()) {
                            modulesNode.forEach(module -> modules.add(module.asText()));
                        }
                    });
                }
            }
        } catch (Exception e) {
            logger.error("Error getting all accessible modules for role {}: {}", roleName, e.getMessage());
        }
        
        return modules;
    }
    
    /**
     * Load role permissions from JSON file
     */
    private JsonNode loadRolePermissions() throws IOException {
        ClassPathResource resource = new ClassPathResource("json/role-feature-permissions.json");
        JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
        return rootNode.get("role_feature_permissions");
    }
    
    /**
     * Role-Feature Permission DTO
     */
    public static class RoleFeaturePermission {
        private RoleName roleName;
        private String featureId;
        private List<PermissionAction> permissions;
        private List<String> accessibleModules;
        
        // Getters and setters
        public RoleName getRoleName() { return roleName; }
        public void setRoleName(RoleName roleName) { this.roleName = roleName; }
        
        public String getFeatureId() { return featureId; }
        public void setFeatureId(String featureId) { this.featureId = featureId; }
        
        public List<PermissionAction> getPermissions() { return permissions; }
        public void setPermissions(List<PermissionAction> permissions) { this.permissions = permissions; }
        
        public List<String> getAccessibleModules() { return accessibleModules; }
        public void setAccessibleModules(List<String> accessibleModules) { this.accessibleModules = accessibleModules; }
    }
}
