package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.PartCategoryRepository;
import com.ascent.solidus.core.domain.module.PartCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class PartCategoryService {
  private final PartCategoryRepository repository;

  public PartCategoryService(PartCategoryRepository repository) {
    this.repository = repository;
  }

  public Page<PartCategory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<PartCategory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public PartCategory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public PartCategory save(PartCategory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
