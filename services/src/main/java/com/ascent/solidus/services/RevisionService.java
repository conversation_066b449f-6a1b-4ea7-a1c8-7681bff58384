package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.RevisionRepository;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class RevisionService {
  private final RevisionRepository repository;

  public RevisionService(RevisionRepository repository) {
    this.repository = repository;
  }

  public Page<Revision> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Revision> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Revision findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Revision save(Revision entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public Product getProductByRevisionId(String id) {
    return repository.getProductByRevisionId(id);
  }

  public List<Revision> findProductByRevisionId(String revisionId) {
    return repository.findProductByRevisionId(revisionId);
  }
}
