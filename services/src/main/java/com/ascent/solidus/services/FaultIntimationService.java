package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationRepository;
import com.ascent.solidus.core.domain.module.FaultIntimation;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationService {
  private final FaultIntimationRepository repository;

  public FaultIntimationService(FaultIntimationRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimation> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimation> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimation findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimation save(FaultIntimation entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
