package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.EscalationEmailTemplateRepository;
import com.ascent.solidus.core.domain.module.EscalationEmailTemplate;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class EscalationEmailTemplateService {
  private final EscalationEmailTemplateRepository repository;

  public EscalationEmailTemplateService(EscalationEmailTemplateRepository repository) {
    this.repository = repository;
  }

  public Page<EscalationEmailTemplate> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<EscalationEmailTemplate> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public EscalationEmailTemplate findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public EscalationEmailTemplate save(EscalationEmailTemplate entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
