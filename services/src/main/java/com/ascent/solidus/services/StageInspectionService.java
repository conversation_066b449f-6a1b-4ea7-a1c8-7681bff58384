package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.StageInspectionRepository;
import com.ascent.solidus.core.domain.module.StageInspection;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class StageInspectionService {
  private final StageInspectionRepository repository;

  public StageInspectionService(StageInspectionRepository repository) {
    this.repository = repository;
  }

  public Page<StageInspection> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<StageInspection> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public StageInspection findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public StageInspection save(StageInspection entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
