package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.StageRepository;
import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Stage;
import com.ascent.solidus.core.domain.module.StageCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class StageService {
  private final StageRepository repository;

  public StageService(StageRepository repository) {
    this.repository = repository;
  }

  public Page<Stage> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Stage> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Stage findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Stage save(Stage entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public Assembly getAssemblyByStageId(String id) {
    return repository.getAssemblyByStageId(id);
  }

  public StageCategory getStageCategoryByStageId(String id) {
    return repository.getStageCategoryByStageId(id);
  }

  public List<Stage> findAssemblyByStageId(String stageId) {
    return repository.findAssemblyByStageId(stageId);
  }
}
