package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SpotBuyDefectRepository;
import com.ascent.solidus.core.domain.module.SpotBuyDefect;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SpotBuyDefectService {
  private final SpotBuyDefectRepository repository;

  public SpotBuyDefectService(SpotBuyDefectRepository repository) {
    this.repository = repository;
  }

  public Page<SpotBuyDefect> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<SpotBuyDefect> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public SpotBuyDefect findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public SpotBuyDefect save(SpotBuyDefect entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
