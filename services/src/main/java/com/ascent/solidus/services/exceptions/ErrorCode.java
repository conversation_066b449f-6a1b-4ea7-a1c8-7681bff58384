package com.ascent.solidus.services.exceptions;

public enum ErrorCode {
    
    // General errors
    INVALID_ARGUMENT("Invalid argument provided"),
    ENTITY_NOT_FOUND("Entity not found"),
    
    // Product catalogue errors
    COUNTRY_ALREADY_EXISTS("Country with this name already exists");
    
    private final String message;
    
    ErrorCode(String message) {
        this.message = message;
    }
    
    public String getMessage() {
        return message;
    }
    
    public static class PoductCatalogueErrors {
        public static final ErrorCode COUNTRY_ALREADY_EXISTS = ErrorCode.COUNTRY_ALREADY_EXISTS;
    }
}