package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NonConformanceMemberRepository;
import com.ascent.solidus.core.domain.module.NonConformanceMember;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NonConformanceMemberService {
  private final NonConformanceMemberRepository repository;

  public NonConformanceMemberService(NonConformanceMemberRepository repository) {
    this.repository = repository;
  }

  public Page<NonConformanceMember> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<NonConformanceMember> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NonConformanceMember findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NonConformanceMember save(NonConformanceMember entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
