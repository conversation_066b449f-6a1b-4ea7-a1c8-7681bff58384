package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;

/**
 * Service interface for managing organization administrators
 */
public interface OrganizationAdminService {

    /**
     * Find all organization admins with pagination and filtering
     *
     * @param searchCriteria The search criteria for pagination and filtering
     * @param loggedInUser The currently logged in user
     * @return A ListResponse containing the organization admins
     */
    ListResponse findAll(SearchCriteria searchCriteria, AppUser loggedInUser);

    /**
     * Save a new organization admin
     *
     * @param appUser The organization admin to save
     * @return The saved organization admin
     */
    AppUser save(AppUser appUser);

    /**
     * Update an existing organization admin
     *
     * @param appUser The organization admin to update
     * @return The updated organization admin
     */
    AppUser update(AppUser appUser);

    /**
     * Get an organization admin by ID
     *
     * @param id The ID of the organization admin
     * @return The organization admin
     */
    AppUser getOne(Long id);
}