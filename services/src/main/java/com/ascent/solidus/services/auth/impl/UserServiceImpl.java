package com.ascent.solidus.services.auth.impl;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.services.auth.UserService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;

@Service

public class UserServiceImpl implements UserService {
    

    @Autowired
    AppUserRepository appUserRepository;
    
    @Override
    @Transactional
    public void updatePassword(String username, String encodedPassword) {
        AppUser user = appUserRepository.findByUsernameAndActive(username, true)
                .orElseThrow(() -> new EntityNotFoundException("User not found with username: " + username));
        
        user.setPassword(encodedPassword);
        appUserRepository.save(user);
    }
} 