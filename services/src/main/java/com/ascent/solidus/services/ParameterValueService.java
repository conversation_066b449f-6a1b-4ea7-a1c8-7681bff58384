package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ParameterValueRepository;
import com.ascent.solidus.core.domain.module.ParameterValue;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ParameterValueService {
  private final ParameterValueRepository repository;

  public ParameterValueService(ParameterValueRepository repository) {
    this.repository = repository;
  }

  public Page<ParameterValue> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ParameterValue> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ParameterValue findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ParameterValue save(ParameterValue entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
