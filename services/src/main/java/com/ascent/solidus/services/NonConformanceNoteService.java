package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NonConformanceNoteRepository;
import com.ascent.solidus.core.domain.module.NonConformanceNote;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NonConformanceNoteService {
  private final NonConformanceNoteRepository repository;

  public NonConformanceNoteService(NonConformanceNoteRepository repository) {
    this.repository = repository;
  }

  public Page<NonConformanceNote> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<NonConformanceNote> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NonConformanceNote findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NonConformanceNote save(NonConformanceNote entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
