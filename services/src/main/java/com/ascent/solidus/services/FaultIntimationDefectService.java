package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationDefectRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationDefect;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationDefectService {
  private final FaultIntimationDefectRepository repository;

  public FaultIntimationDefectService(FaultIntimationDefectRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationDefect> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationDefect> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationDefect findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationDefect save(FaultIntimationDefect entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
