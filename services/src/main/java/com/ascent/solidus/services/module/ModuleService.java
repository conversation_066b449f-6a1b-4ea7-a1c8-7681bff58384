//package com.ascent.solidus.services.module;
//
//import com.ascent.solidus.core.domain.module.Module;
//import com.ascent.solidus.core.repository.module.ModuleRepository;
//import jakarta.persistence.EntityNotFoundException;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.Assert;
//
//import java.util.List;
//
//@Service
//@RequiredArgsConstructor
//@Slf4j
//public class ModuleService {
//
//    private final ModuleRepository moduleRepository;
//
//    @Transactional(readOnly = true)
//    public List<Module> findAllStandaloneModules() {
//        return moduleRepository.findByIsStandaloneTrue();
//    }
//
//    @Transactional(readOnly = true)
//    public List<Module> findAllModules() {
//        return moduleRepository.findAll();
//    }
//
//    @Transactional(readOnly = true)
//    public List<Module> findAllComponentModules() {
//        return moduleRepository.findByIsComponentTrue();
//    }
//
//    @Transactional(readOnly = true)
//    public Module findById(Long id) {
//        return moduleRepository.findById(id)
//                .orElseThrow(() -> new EntityNotFoundException("Module not found with id: " + id));
//    }
//
//    @Transactional(readOnly = true)
//    public Module findByName(String name) {
//        return moduleRepository.findByName(name)
//                .orElseThrow(() -> new EntityNotFoundException("Module not found with name: " + name));
//    }
//
//    @Transactional
//    public Module createStandaloneModule(Module module) {
//        Assert.notNull(module, "Module must not be null");
//        module.setStandalone(true);
//        return moduleRepository.save(module);
//    }
//
//    @Transactional
//    public Module createComponentModule(Module module) {
//        Assert.notNull(module, "Module must not be null");
//
//        if (module.getParent() != null) {
//            Module parent = findById(module.getParent().getId());
//            parent.setStandalone(true);
//            moduleRepository.save(parent);
//        }
//
//        module.setComponent(true);
//        return moduleRepository.save(module);
//    }
//
//    @Transactional
//    public Module updateModule(Long id, Module module) {
//        Assert.notNull(module, "Module must not be null");
//
//        Module existingModule = findById(id);
//        existingModule.setName(module.getName());
//        existingModule.setDescription(module.getDescription());
//        existingModule.setIcon(module.getIcon());
//
//        if (module.getParent() != null && !module.getParent().equals(existingModule.getParent())) {
//            Module parent = findById(module.getParent().getId());
//            existingModule.setParent(parent);
//            parent.setStandalone(true);
//            moduleRepository.save(parent);
//        }
//
//        return moduleRepository.save(existingModule);
//    }
//
//    @Transactional
//    public void deleteModule(Long id) {
//        Module module = findById(id);
//        moduleRepository.delete(module);
//    }
//
//    @Transactional(readOnly = true)
//    public List<Module> findSubModules(Long parentId) {
//        return moduleRepository.findByParentId(parentId);
//    }
//}