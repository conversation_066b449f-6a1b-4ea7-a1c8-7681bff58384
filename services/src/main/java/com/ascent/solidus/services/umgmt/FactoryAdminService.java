package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.core.domain.module.AppUser;


/**
 * Service interface for managing factory administrators
 */
public interface FactoryAdminService {
    
    /**
     * Find all factory admins with pagination and filtering
     * 
     * @param searchCriteria The search criteria for pagination and filtering
     * @param loggedInUser The currently logged in user
     * @return A ListResponse containing the factory admins
     */
    ListResponse findAll(SearchCriteria searchCriteria, AppUser loggedInUser);
    
    /**
     * Save a new factory admin
     * 
     * @param appUser The factory admin to save
     * @return The saved factory admin
     */
    AppUser save(AppUser appUser);
    
    /**
     * Update an existing factory admin
     * 
     * @param appUser The factory admin to update
     * @return The updated factory admin
     */
    AppUser update(AppUser appUser);
    
    /**
     * Get a factory admin by ID
     * 
     * @param id The ID of the factory admin
     * @return The factory admin
     */
    AppUser getOne(Long id);
    
    /**
     * Resend credentials email to a factory admin
     * 
     * @param id The ID of the factory admin
     * @return The factory admin
     */
    AppUser resendMail(Long id);
}