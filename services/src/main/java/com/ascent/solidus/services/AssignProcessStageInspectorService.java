package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignProcessStageInspectorRepository;
import com.ascent.solidus.core.domain.module.AssignProcessStageInspector;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignProcessStageInspectorService {
  private final AssignProcessStageInspectorRepository repository;

  public AssignProcessStageInspectorService(AssignProcessStageInspectorRepository repository) {
    this.repository = repository;
  }

  public Page<AssignProcessStageInspector> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignProcessStageInspector> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignProcessStageInspector findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignProcessStageInspector save(AssignProcessStageInspector entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
