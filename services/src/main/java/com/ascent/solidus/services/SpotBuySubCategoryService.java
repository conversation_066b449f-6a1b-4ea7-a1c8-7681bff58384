package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SpotBuySubCategoryRepository;
import com.ascent.solidus.core.domain.module.SpotBuySubCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SpotBuySubCategoryService {
  private final SpotBuySubCategoryRepository repository;

  public SpotBuySubCategoryService(SpotBuySubCategoryRepository repository) {
    this.repository = repository;
  }

  public Page<SpotBuySubCategory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<SpotBuySubCategory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public SpotBuySubCategory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public SpotBuySubCategory save(SpotBuySubCategory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
