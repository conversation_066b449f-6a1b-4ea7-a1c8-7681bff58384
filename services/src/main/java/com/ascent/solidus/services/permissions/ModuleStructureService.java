package com.ascent.solidus.services.permissions;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * Service to get module structure from your existing module-config.json files
 * This provides the data for the module tree in the UI
 */
@Service
public class ModuleStructureService {
    
    private static final Logger logger = LoggerFactory.getLogger(ModuleStructureService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Get all available modules in a hierarchical structure
     * This matches the tree structure shown in your UI
     */
    public List<ModuleStructure> getAllModuleStructures() {
        List<ModuleStructure> moduleStructures = new ArrayList<>();
        
        try {
            // Load from core module-config.json
            moduleStructures.addAll(loadModuleStructureFromFile("module-config.json"));
            
            // Load from services module-config.json
            moduleStructures.addAll(loadModuleStructureFromFile("module-config.json", "services/src/main/resources/"));
            
        } catch (Exception e) {
            logger.error("Error loading module structures: {}", e.getMessage());
        }
        
        return moduleStructures;
    }
    
    /**
     * Load module structure from a specific config file
     */
    private List<ModuleStructure> loadModuleStructureFromFile(String fileName) throws IOException {
        return loadModuleStructureFromFile(fileName, "");
    }
    
    private List<ModuleStructure> loadModuleStructureFromFile(String fileName, String pathPrefix) throws IOException {
        List<ModuleStructure> structures = new ArrayList<>();
        
        try {
            ClassPathResource resource = new ClassPathResource(pathPrefix + fileName);
            if (!resource.exists()) {
                logger.warn("Module config file not found: {}", pathPrefix + fileName);
                return structures;
            }
            
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            
            // Get parent module info
            String parentModuleId = rootNode.get("moduleId").asText();
            String parentModuleName = rootNode.get("moduleName").asText();
            
            // Create parent module structure
            ModuleStructure parentStructure = new ModuleStructure();
            parentStructure.setModuleName(parentModuleName);
            parentStructure.setModuleId(parentModuleId);
            parentStructure.setParentModule(true);
            parentStructure.setDescription(parentModuleName + " module");
            parentStructure.setSubModules(new ArrayList<>());
            
            // Get sub-modules
            JsonNode modulesNode = rootNode.get("modules");
            if (modulesNode != null && modulesNode.isArray()) {
                for (JsonNode moduleNode : modulesNode) {
                    String subModuleName = moduleNode.get("moduleName").asText();
                    String subModuleId = moduleNode.get("moduleId").asText();
                    
                    ModuleStructure subStructure = new ModuleStructure();
                    subStructure.setModuleName(subModuleName);
                    subStructure.setModuleId(subModuleId);
                    subStructure.setParentModule(false);
                    subStructure.setParentModuleName(parentModuleName);
                    subStructure.setDescription(subModuleName + " description");
                    
                    parentStructure.getSubModules().add(subStructure);
                }
            }
            
            structures.add(parentStructure);
            
        } catch (IOException e) {
            logger.error("Error reading module config file {}: {}", pathPrefix + fileName, e.getMessage());
            throw e;
        }
        
        return structures;
    }
    
    /**
     * Get flattened list of all modules (for permission assignment)
     */
    public List<ModuleInfo> getAllModulesFlattened() {
        List<ModuleInfo> allModules = new ArrayList<>();
        List<ModuleStructure> structures = getAllModuleStructures();
        
        for (ModuleStructure structure : structures) {
            // Add parent module
            allModules.add(new ModuleInfo(
                structure.getModuleName(),
                structure.getModuleId(),
                null,
                true,
                structure.getDescription()
            ));
            
            // Add sub-modules
            for (ModuleStructure subModule : structure.getSubModules()) {
                allModules.add(new ModuleInfo(
                    subModule.getModuleName(),
                    subModule.getModuleId(),
                    structure.getModuleName(),
                    false,
                    subModule.getDescription()
                ));
            }
        }
        
        return allModules;
    }
    
    /**
     * Check if a module exists
     */
    public boolean moduleExists(String moduleName) {
        return getAllModulesFlattened().stream()
                .anyMatch(module -> module.getModuleName().equals(moduleName));
    }
    
    /**
     * Get parent module name for a sub-module
     */
    public String getParentModuleName(String subModuleName) {
        return getAllModulesFlattened().stream()
                .filter(module -> module.getModuleName().equals(subModuleName) && !module.isParentModule())
                .map(ModuleInfo::getParentModuleName)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Module structure class for hierarchical representation
     */
    public static class ModuleStructure {
        private String moduleName;
        private String moduleId;
        private boolean isParentModule;
        private String parentModuleName;
        private String description;
        private List<ModuleStructure> subModules;
        
        // Getters and Setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }
        
        public String getModuleId() { return moduleId; }
        public void setModuleId(String moduleId) { this.moduleId = moduleId; }
        
        public boolean isParentModule() { return isParentModule; }
        public void setParentModule(boolean parentModule) { isParentModule = parentModule; }
        
        public String getParentModuleName() { return parentModuleName; }
        public void setParentModuleName(String parentModuleName) { this.parentModuleName = parentModuleName; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<ModuleStructure> getSubModules() { return subModules; }
        public void setSubModules(List<ModuleStructure> subModules) { this.subModules = subModules; }
    }
    
    /**
     * Flattened module info class
     */
    public static class ModuleInfo {
        private String moduleName;
        private String moduleId;
        private String parentModuleName;
        private boolean isParentModule;
        private String description;
        
        public ModuleInfo(String moduleName, String moduleId, String parentModuleName, boolean isParentModule, String description) {
            this.moduleName = moduleName;
            this.moduleId = moduleId;
            this.parentModuleName = parentModuleName;
            this.isParentModule = isParentModule;
            this.description = description;
        }
        
        // Getters and Setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }
        
        public String getModuleId() { return moduleId; }
        public void setModuleId(String moduleId) { this.moduleId = moduleId; }
        
        public String getParentModuleName() { return parentModuleName; }
        public void setParentModuleName(String parentModuleName) { this.parentModuleName = parentModuleName; }
        
        public boolean isParentModule() { return isParentModule; }
        public void setParentModule(boolean parentModule) { isParentModule = parentModule; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
