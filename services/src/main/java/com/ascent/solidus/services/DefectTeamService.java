package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DefectTeamRepository;
import com.ascent.solidus.core.domain.module.DefectTeam;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DefectTeamService {
  private final DefectTeamRepository repository;

  public DefectTeamService(DefectTeamRepository repository) {
    this.repository = repository;
  }

  public Page<DefectTeam> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DefectTeam> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DefectTeam findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DefectTeam save(DefectTeam entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
