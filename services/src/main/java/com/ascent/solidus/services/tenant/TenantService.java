package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.repository.tenant.TenantRepository;
import com.ascent.solidus.core.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class TenantService {


    @Autowired
    TenantRepository tenantRepository;

    public Tenant getCurrentTenant() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long tenantId = userPrincipal.getTenantId();
            return tenantRepository.findById(tenantId)
                    .orElseThrow(() -> new RuntimeException("Tenant not found"));
        }
//        log.error("No authenticated user found or principal is not UserPrincipal");
        throw new RuntimeException("No authenticated user found");
    }

    public Tenant findById(Long id) {
        return tenantRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Tenant not found with id: " + id));
    }
}
