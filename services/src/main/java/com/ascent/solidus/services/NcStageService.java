package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NcStageRepository;
import com.ascent.solidus.core.domain.module.NcStage;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NcStageService {
  private final NcStageRepository repository;

  public NcStageService(NcStageRepository repository) {
    this.repository = repository;
  }

  public Page<NcStage> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<NcStage> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NcStage findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NcStage save(NcStage entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
