package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DashboardLimitRepository;
import com.ascent.solidus.core.domain.module.DashboardLimit;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DashboardLimitService {
  private final DashboardLimitRepository repository;

  public DashboardLimitService(DashboardLimitRepository repository) {
    this.repository = repository;
  }

  public Page<DashboardLimit> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DashboardLimit> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DashboardLimit findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DashboardLimit save(DashboardLimit entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
