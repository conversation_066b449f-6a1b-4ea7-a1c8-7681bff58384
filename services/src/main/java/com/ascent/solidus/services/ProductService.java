package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProductRepository;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.ProductFamily;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProductService {
  private final ProductRepository repository;

  public ProductService(ProductRepository repository) {
    this.repository = repository;
  }

  public Page<Product> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Product> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Product findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Product save(Product entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public ProductFamily getProductFamilyByProductId(String id) {
    return repository.getProductFamilyByProductId(id);
  }

  public List<Product> findProductFamilyByProductId(String productId) {
    return repository.findProductFamilyByProductId(productId);
  }
}
