package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DashboardLimitValueRepository;
import com.ascent.solidus.core.domain.module.DashboardLimitValue;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DashboardLimitValueService {
  private final DashboardLimitValueRepository repository;

  public DashboardLimitValueService(DashboardLimitValueRepository repository) {
    this.repository = repository;
  }

  public Page<DashboardLimitValue> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DashboardLimitValue> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DashboardLimitValue findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DashboardLimitValue save(DashboardLimitValue entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
