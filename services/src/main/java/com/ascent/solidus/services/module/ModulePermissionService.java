//package com.ascent.solidus.services.module;
//
//import com.ascent.solidus.core.constants.RoleName;
//import com.ascent.solidus.core.domain.module.Module;
//import com.ascent.solidus.core.domain.module.ModulePermission;
//import com.ascent.solidus.core.domain.umgmt.Role;
//import com.ascent.solidus.core.repository.module.ModulePermissionRepository;
//import com.ascent.solidus.core.repository.module.ModuleRepository;
//import com.ascent.solidus.core.repository.umgmt.RoleRepository;
//import jakarta.persistence.EntityNotFoundException;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.Assert;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@Service
//@RequiredArgsConstructor
//@Slf4j
//public class ModulePermissionService {
//
//    private final ModulePermissionRepository modulePermissionRepository;
//    private final RoleRepository roleRepository;
//    private final ModuleRepository moduleRepository;
//
//    @Transactional(readOnly = true)
//    public List<ModulePermission> findByRole(RoleName roleName) {
//        Role role = roleRepository.findByName(roleName)
//                .orElseThrow(() -> new EntityNotFoundException("Role not found: " + roleName));
//
//        return modulePermissionRepository.findByRoleAndActive(role, true);
//    }
//
//    @Transactional(readOnly = true)
//    public List<Long> getModuleIdsByRole(RoleName roleName) {
//        return modulePermissionRepository.findModuleIdsByRoleName(roleName);
//    }
//
//    @Transactional
//    public void assignModulesToRole(RoleName roleName, List<Long> moduleIds) {
//        Role role = roleRepository.findByName(roleName)
//                .orElseThrow(() -> new EntityNotFoundException("Role not found: " + roleName));
//
//        // First, deactivate all existing permissions for this role
//        List<ModulePermission> existingPermissions = modulePermissionRepository.findByRoleAndActive(role, true);
//        for (ModulePermission permission : existingPermissions) {
//            permission.setActive(false);
//            modulePermissionRepository.save(permission);
//        }
//
//        // Then, create new permissions for the selected modules
//        for (Long moduleId : moduleIds) {
//            Module module = moduleRepository.findById(moduleId)
//                    .orElseThrow(() -> new EntityNotFoundException("Module not found with id: " + moduleId));
//
//            Optional<ModulePermission> existingPermission =
//                    modulePermissionRepository.findByRoleAndModuleAndActive(role, module, false);
//
//            if (existingPermission.isPresent()) {
//                ModulePermission permission = existingPermission.get();
//                permission.setActive(true);
//                modulePermissionRepository.save(permission);
//            } else {
//                ModulePermission newPermission = new ModulePermission();
//                newPermission.setRole(role);
//                newPermission.setModule(module);
//                newPermission.setActive(true);
//                modulePermissionRepository.save(newPermission);
//            }
//        }
//    }
//
//    @Transactional
//    public void assignModuleWithSubmodulesToRole(RoleName roleName, Long parentModuleId, List<Long> subModuleIds) {
//        Role role = roleRepository.findByName(roleName)
//                .orElseThrow(() -> new EntityNotFoundException("Role not found: " + roleName));
//
//        Module parentModule = moduleRepository.findById(parentModuleId)
//                .orElseThrow(() -> new EntityNotFoundException("Parent module not found with id: " + parentModuleId));
//
//        // Assign parent module
//        assignModuleToRole(role, parentModule);
//
//        // Assign selected submodules
//        if (subModuleIds != null && !subModuleIds.isEmpty()) {
//            for (Long subModuleId : subModuleIds) {
//                Module subModule = moduleRepository.findById(subModuleId)
//                        .orElseThrow(() -> new EntityNotFoundException("Submodule not found with id: " + subModuleId));
//
//                assignModuleToRole(role, subModule);
//            }
//        }
//    }
//
//    private void assignModuleToRole(Role role, Module module) {
//        Optional<ModulePermission> existingPermission =
//                modulePermissionRepository.findByRoleAndModuleAndActive(role, module, true);
//
//        if (existingPermission.isEmpty()) {
//            ModulePermission newPermission = new ModulePermission();
//            newPermission.setRole(role);
//            newPermission.setModule(module);
//            newPermission.setActive(true);
//            modulePermissionRepository.save(newPermission);
//        }
//    }
//}