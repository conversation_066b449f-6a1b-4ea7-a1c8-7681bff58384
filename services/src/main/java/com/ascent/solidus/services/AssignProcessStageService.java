package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignProcessStageRepository;
import com.ascent.solidus.core.domain.module.AssignProcessStage;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignProcessStageService {
  private final AssignProcessStageRepository repository;

  public AssignProcessStageService(AssignProcessStageRepository repository) {
    this.repository = repository;
  }

  public Page<AssignProcessStage> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignProcessStage> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignProcessStage findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignProcessStage save(AssignProcessStage entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
