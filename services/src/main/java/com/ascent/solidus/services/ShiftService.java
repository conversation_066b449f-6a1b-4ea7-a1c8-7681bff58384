package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ShiftRepository;
import com.ascent.solidus.core.domain.module.Shift;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ShiftService {
  private final ShiftRepository repository;

  public ShiftService(ShiftRepository repository) {
    this.repository = repository;
  }

  public Page<Shift> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Shift> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Shift findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Shift save(Shift entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
