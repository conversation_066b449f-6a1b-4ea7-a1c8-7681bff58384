package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.StageInspectionRoundRepository;
import com.ascent.solidus.core.domain.module.StageInspectionRound;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class StageInspectionRoundService {
  private final StageInspectionRoundRepository repository;

  public StageInspectionRoundService(StageInspectionRoundRepository repository) {
    this.repository = repository;
  }

  public Page<StageInspectionRound> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<StageInspectionRound> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public StageInspectionRound findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public StageInspectionRound save(StageInspectionRound entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
