package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SpotBuyCategoryRepository;
import com.ascent.solidus.core.domain.module.SpotBuyCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SpotBuyCategoryService {
  private final SpotBuyCategoryRepository repository;

  public SpotBuyCategoryService(SpotBuyCategoryRepository repository) {
    this.repository = repository;
  }

  public Page<SpotBuyCategory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<SpotBuyCategory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public SpotBuyCategory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public SpotBuyCategory save(SpotBuyCategory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
