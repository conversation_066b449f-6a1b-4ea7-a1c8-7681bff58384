package com.ascent.solidus.services.permissions;

import com.ascent.solidus.core.constants.RoleName;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * JSON-based role permission service - NO parent names stored in database
 * All module hierarchy and permissions are managed through JSON files
 */
@Service
public class JsonBasedRolePermissionService {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonBasedRolePermissionService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Check if role has access to a module (from JSON)
     */
    public boolean hasRoleAccessToModule(RoleName roleName, String moduleName) {
        try {
            Map<String, Object> rolePermissions = loadRolePermissions();
            
            if (rolePermissions.containsKey(roleName.name())) {
                Map<String, Object> roleData = (Map<String, Object>) rolePermissions.get(roleName.name());
                List<String> accessibleModules = (List<String>) roleData.get("accessible_modules");
                
                return accessibleModules != null && accessibleModules.contains(moduleName);
            }
            
        } catch (Exception e) {
            logger.error("Error checking role access: {}", e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Check if role has specific permission for a module (from JSON)
     */
    public boolean hasRolePermissionForModule(RoleName roleName, String moduleName, String permissionType) {
        try {
            Map<String, Object> rolePermissions = loadRolePermissions();
            
            if (rolePermissions.containsKey(roleName.name())) {
                Map<String, Object> roleData = (Map<String, Object>) rolePermissions.get(roleName.name());
                Map<String, List<String>> permissions = (Map<String, List<String>>) roleData.get("permissions");
                
                if (permissions != null && permissions.containsKey(moduleName)) {
                    List<String> modulePermissions = permissions.get(moduleName);
                    return modulePermissions.contains(permissionType.toUpperCase());
                }
            }
            
        } catch (Exception e) {
            logger.error("Error checking role permission: {}", e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Get all accessible modules for a role (from JSON)
     */
    public List<String> getAccessibleModulesForRole(RoleName roleName) {
        try {
            Map<String, Object> rolePermissions = loadRolePermissions();
            
            if (rolePermissions.containsKey(roleName.name())) {
                Map<String, Object> roleData = (Map<String, Object>) rolePermissions.get(roleName.name());
                List<String> accessibleModules = (List<String>) roleData.get("accessible_modules");
                
                return accessibleModules != null ? accessibleModules : new ArrayList<>();
            }
            
        } catch (Exception e) {
            logger.error("Error getting accessible modules: {}", e.getMessage());
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Get module hierarchy with permissions for UI (combines JSON configs)
     */
    public List<ModuleHierarchy> getModuleHierarchyForRole(RoleName roleName) {
        List<ModuleHierarchy> hierarchy = new ArrayList<>();
        
        try {
            // Get module structure from your existing module-config.json files
            List<ModuleStructure> moduleStructures = loadModuleStructures();
            
            // Get role permissions from JSON
            List<String> accessibleModules = getAccessibleModulesForRole(roleName);
            Map<String, List<String>> rolePermissions = getRolePermissionsMap(roleName);
            
            // Build hierarchy for UI
            for (ModuleStructure structure : moduleStructures) {
                ModuleHierarchy parentHierarchy = new ModuleHierarchy();
                parentHierarchy.setModuleName(structure.getParentModuleName());
                parentHierarchy.setIsParentModule(true);
                parentHierarchy.setChecked(false); // Parent modules are not directly checkable
                parentHierarchy.setChildren(new ArrayList<>());
                
                // Add child modules
                for (String childModule : structure.getChildModules()) {
                    ModuleHierarchy childHierarchy = new ModuleHierarchy();
                    childHierarchy.setModuleName(childModule);
                    childHierarchy.setIsParentModule(false);
                    childHierarchy.setChecked(accessibleModules.contains(childModule));
                    
                    // Set permissions
                    if (rolePermissions.containsKey(childModule)) {
                        List<String> permissions = rolePermissions.get(childModule);
                        childHierarchy.setCanRead(permissions.contains("READ"));
                        childHierarchy.setCanWrite(permissions.contains("WRITE"));
                        childHierarchy.setCanDelete(permissions.contains("DELETE"));
                        childHierarchy.setCanExecute(permissions.contains("EXECUTE"));
                    }
                    
                    parentHierarchy.getChildren().add(childHierarchy);
                }
                
                hierarchy.add(parentHierarchy);
            }
            
        } catch (Exception e) {
            logger.error("Error building module hierarchy: {}", e.getMessage());
        }
        
        return hierarchy;
    }
    
    /**
     * Save role permissions (updates JSON file)
     */
    public void saveRolePermissions(RoleName roleName, List<ModulePermissionUpdate> updates) {
        try {
            Map<String, Object> allRolePermissions = loadRolePermissions();
            
            // Update permissions for this role
            Map<String, Object> roleData = new HashMap<>();
            List<String> accessibleModules = new ArrayList<>();
            Map<String, List<String>> permissions = new HashMap<>();
            
            for (ModulePermissionUpdate update : updates) {
                if (update.isChecked()) {
                    accessibleModules.add(update.getModuleName());
                    
                    List<String> modulePermissions = new ArrayList<>();
                    if (update.isCanRead()) modulePermissions.add("READ");
                    if (update.isCanWrite()) modulePermissions.add("WRITE");
                    if (update.isCanDelete()) modulePermissions.add("DELETE");
                    if (update.isCanExecute()) modulePermissions.add("EXECUTE");
                    
                    permissions.put(update.getModuleName(), modulePermissions);
                }
            }
            
            roleData.put("accessible_modules", accessibleModules);
            roleData.put("permissions", permissions);
            allRolePermissions.put(roleName.name(), roleData);
            
            // Save back to JSON file (in a real system, you might want to use a different approach)
            logger.info("Role permissions updated for {}: {} modules", roleName, accessibleModules.size());
            
        } catch (Exception e) {
            logger.error("Error saving role permissions: {}", e.getMessage());
        }
    }
    
    // Helper methods
    private Map<String, Object> loadRolePermissions() throws IOException {
        ClassPathResource resource = new ClassPathResource("json/role-module-permissions-simple.json");
        JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
        JsonNode rolePermissions = rootNode.get("role_permissions");
        
        return objectMapper.convertValue(rolePermissions, Map.class);
    }
    
    private Map<String, List<String>> getRolePermissionsMap(RoleName roleName) {
        try {
            Map<String, Object> rolePermissions = loadRolePermissions();
            if (rolePermissions.containsKey(roleName.name())) {
                Map<String, Object> roleData = (Map<String, Object>) rolePermissions.get(roleName.name());
                return (Map<String, List<String>>) roleData.get("permissions");
            }
        } catch (Exception e) {
            logger.error("Error getting role permissions map: {}", e.getMessage());
        }
        return new HashMap<>();
    }
    
    private List<ModuleStructure> loadModuleStructures() {
        // This reads from your existing module-config.json files
        // and extracts parent-child relationships
        List<ModuleStructure> structures = new ArrayList<>();
        
        try {
            // Load from core/module-config.json
            ClassPathResource resource = new ClassPathResource("module-config.json");
            if (resource.exists()) {
                JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
                String parentName = rootNode.get("moduleName").asText();
                
                ModuleStructure structure = new ModuleStructure();
                structure.setParentModuleName(parentName);
                structure.setChildModules(new ArrayList<>());
                
                JsonNode modules = rootNode.get("modules");
                if (modules != null && modules.isArray()) {
                    for (JsonNode module : modules) {
                        structure.getChildModules().add(module.get("moduleName").asText());
                    }
                }
                
                structures.add(structure);
            }
            
            // Load from services/src/main/resources/module-config.json
            ClassPathResource servicesResource = new ClassPathResource("module-config.json");
            if (servicesResource.exists()) {
                // Similar logic for services module config
            }
            
        } catch (Exception e) {
            logger.error("Error loading module structures: {}", e.getMessage());
        }
        
        return structures;
    }
    
    // DTOs
    public static class ModuleHierarchy {
        private String moduleName;
        private boolean isParentModule;
        private boolean checked;
        private boolean canRead;
        private boolean canWrite;
        private boolean canDelete;
        private boolean canExecute;
        private List<ModuleHierarchy> children;
        
        // Getters and setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }
        
        public boolean isParentModule() { return isParentModule; }
        public void setIsParentModule(boolean parentModule) { isParentModule = parentModule; }
        
        public boolean isChecked() { return checked; }
        public void setChecked(boolean checked) { this.checked = checked; }
        
        public boolean isCanRead() { return canRead; }
        public void setCanRead(boolean canRead) { this.canRead = canRead; }
        
        public boolean isCanWrite() { return canWrite; }
        public void setCanWrite(boolean canWrite) { this.canWrite = canWrite; }
        
        public boolean isCanDelete() { return canDelete; }
        public void setCanDelete(boolean canDelete) { this.canDelete = canDelete; }
        
        public boolean isCanExecute() { return canExecute; }
        public void setCanExecute(boolean canExecute) { this.canExecute = canExecute; }
        
        public List<ModuleHierarchy> getChildren() { return children; }
        public void setChildren(List<ModuleHierarchy> children) { this.children = children; }
    }
    
    public static class ModuleStructure {
        private String parentModuleName;
        private List<String> childModules;
        
        public String getParentModuleName() { return parentModuleName; }
        public void setParentModuleName(String parentModuleName) { this.parentModuleName = parentModuleName; }
        
        public List<String> getChildModules() { return childModules; }
        public void setChildModules(List<String> childModules) { this.childModules = childModules; }
    }
    
    public static class ModulePermissionUpdate {
        private String moduleName;
        private boolean checked;
        private boolean canRead;
        private boolean canWrite;
        private boolean canDelete;
        private boolean canExecute;
        
        // Getters and setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }
        
        public boolean isChecked() { return checked; }
        public void setChecked(boolean checked) { this.checked = checked; }
        
        public boolean isCanRead() { return canRead; }
        public void setCanRead(boolean canRead) { this.canRead = canRead; }
        
        public boolean isCanWrite() { return canWrite; }
        public void setCanWrite(boolean canWrite) { this.canWrite = canWrite; }
        
        public boolean isCanDelete() { return canDelete; }
        public void setCanDelete(boolean canDelete) { this.canDelete = canDelete; }
        
        public boolean isCanExecute() { return canExecute; }
        public void setCanExecute(boolean canExecute) { this.canExecute = canExecute; }
    }
}
