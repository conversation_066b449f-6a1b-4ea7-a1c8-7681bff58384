package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.WorkInstructionRepository;
import com.ascent.solidus.core.domain.module.WorkInstruction;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class WorkInstructionService {
  private final WorkInstructionRepository repository;

  public WorkInstructionService(WorkInstructionRepository repository) {
    this.repository = repository;
  }

  public Page<WorkInstruction> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<WorkInstruction> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public WorkInstruction findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public WorkInstruction save(WorkInstruction entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
