package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.BatchRepository;
import com.ascent.solidus.core.domain.module.Batch;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class BatchService {
  private final BatchRepository repository;

  public BatchService(BatchRepository repository) {
    this.repository = repository;
  }

  public Page<Batch> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Batch> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Batch findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Batch save(Batch entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
