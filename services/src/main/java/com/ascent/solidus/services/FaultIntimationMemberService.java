package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationMemberRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationMember;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationMemberService {
  private final FaultIntimationMemberRepository repository;

  public FaultIntimationMemberService(FaultIntimationMemberRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationMember> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationMember> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationMember findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationMember save(FaultIntimationMember entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
