package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.WorkOrderRepository;
import com.ascent.solidus.core.domain.module.WorkOrder;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class WorkOrderService {
  private final WorkOrderRepository repository;

  public WorkOrderService(WorkOrderRepository repository) {
    this.repository = repository;
  }

  public Page<WorkOrder> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<WorkOrder> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public WorkOrder findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public WorkOrder save(WorkOrder entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
