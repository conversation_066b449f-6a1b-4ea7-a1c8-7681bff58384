package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DefectTypeRepository;
import com.ascent.solidus.core.domain.module.DefectType;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DefectTypeService {
  private final DefectTypeRepository repository;

  public DefectTypeService(DefectTypeRepository repository) {
    this.repository = repository;
  }

  public Page<DefectType> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DefectType> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DefectType findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DefectType save(DefectType entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
