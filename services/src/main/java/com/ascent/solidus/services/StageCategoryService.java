package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.StageCategoryRepository;
import com.ascent.solidus.core.domain.module.StageCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class StageCategoryService {
  private final StageCategoryRepository repository;

  public StageCategoryService(StageCategoryRepository repository) {
    this.repository = repository;
  }

  public Page<StageCategory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<StageCategory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public StageCategory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public StageCategory save(StageCategory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
