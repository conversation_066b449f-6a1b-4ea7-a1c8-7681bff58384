package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NonConformanceTeamRepository;
import com.ascent.solidus.core.domain.module.NonConformanceTeam;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NonConformanceTeamService {
  private final NonConformanceTeamRepository repository;

  public NonConformanceTeamService(NonConformanceTeamRepository repository) {
    this.repository = repository;
  }

  public Page<NonConformanceTeam> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<NonConformanceTeam> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NonConformanceTeam findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NonConformanceTeam save(NonConformanceTeam entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
