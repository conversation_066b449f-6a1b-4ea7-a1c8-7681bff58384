package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DateWaiseParameterRepository;
import com.ascent.solidus.core.domain.module.DateWaiseParameter;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DateWaiseParameterService {
  private final DateWaiseParameterRepository repository;

  public DateWaiseParameterService(DateWaiseParameterRepository repository) {
    this.repository = repository;
  }

  public Page<DateWaiseParameter> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DateWaiseParameter> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DateWaiseParameter findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DateWaiseParameter save(DateWaiseParameter entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
