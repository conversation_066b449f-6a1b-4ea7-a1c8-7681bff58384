package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ManufacturingPartRepository;
import com.ascent.solidus.core.domain.module.ManufacturingPart;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ManufacturingPartService {
  private final ManufacturingPartRepository repository;

  public ManufacturingPartService(ManufacturingPartRepository repository) {
    this.repository = repository;
  }

  public Page<ManufacturingPart> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ManufacturingPart> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ManufacturingPart findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ManufacturingPart save(ManufacturingPart entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
