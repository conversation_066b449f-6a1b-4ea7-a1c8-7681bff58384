package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.InspectionRepository;
import com.ascent.solidus.core.domain.module.Inspection;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class InspectionService {
  private final InspectionRepository repository;

  public InspectionService(InspectionRepository repository) {
    this.repository = repository;
  }

  public Page<Inspection> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Inspection> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Inspection findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Inspection save(Inspection entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
