package com.ascent.solidus.services.module;

import java.util.List;
import java.util.Map;

public interface DynamicEntityService {

    /**
     * Creates a new entity of the specified type
     */
    Object create(String entityType, Map<String, Object> fieldValues);

    /**
     * Finds an entity by ID
     */
    Object findById(String entityType, String id) ;

    /**
     * Finds all entities of a type
     */
    List<Object> findAll(String entityType);

    /**
     * Updates an entity
     */
    Object update(String entityType, String id, Map<String, Object> updates)
            ;

    /**
     * Deletes an entity
     */
    void delete(String entityType, String id) ;

    /**
     * Checks if entity type is registered
     */
    boolean isEntityTypeRegistered(String entityType);

    List<Object> findByParentModule(String entityType, String parentModuleId) throws Exception;




}