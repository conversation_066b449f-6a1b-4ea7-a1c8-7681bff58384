package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DefectGroupRepository;
import com.ascent.solidus.core.domain.module.DefectGroup;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DefectGroupService {
  private final DefectGroupRepository repository;

  public DefectGroupService(DefectGroupRepository repository) {
    this.repository = repository;
  }

  public Page<DefectGroup> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DefectGroup> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DefectGroup findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DefectGroup save(DefectGroup entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
