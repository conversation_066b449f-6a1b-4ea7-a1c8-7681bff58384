package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.MaterialGroupRepository;
import com.ascent.solidus.core.domain.module.MaterialGroup;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class MaterialGroupService {
  private final MaterialGroupRepository repository;

  public MaterialGroupService(MaterialGroupRepository repository) {
    this.repository = repository;
  }

  public Page<MaterialGroup> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<MaterialGroup> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public MaterialGroup findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public MaterialGroup save(MaterialGroup entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
