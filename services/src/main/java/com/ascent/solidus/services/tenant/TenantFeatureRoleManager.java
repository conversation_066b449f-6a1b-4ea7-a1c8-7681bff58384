package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Utility class for managing tenant-feature-role relationships
 */
@Component
public class TenantFeatureRoleManager {
    
    @Autowired
    private FeatureAccessControlService featureAccessControlService;
    
    @Autowired
    private TenantFeatureService tenantFeatureService;
    
    @Autowired
    private RoleFeaturePermissionService roleFeaturePermissionService;
    
    @Autowired
    private TenantModuleConfigService tenantModuleConfigService;
    
    /**
     * Get all features accessible to a user
     */
    public List<String> getUserAccessibleFeatures(Long userId) {
        return featureAccessControlService.getUserAccessibleFeatures(userId);
    }
    
    /**
     * Get all modules within a feature that a user can access
     */
    public List<String> getUserAccessibleModules(Long userId, String featureId) {
        return featureAccessControlService.getUserAccessibleModules(userId, featureId);
    }
    
    /**
     * Get user's permissions for a specific feature
     */
    public List<PermissionAction> getUserFeaturePermissions(Long userId, String featureId) {
        return featureAccessControlService.getUserPermissionsForFeature(userId, featureId);
    }
    
    /**
     * Check if user can perform a specific action on a feature
     */
    public boolean canUserPerformAction(Long userId, String featureId, PermissionAction action) {
        return featureAccessControlService.hasUserPermissionForFeature(userId, featureId, action);
    }
    
    /**
     * Check if user can access a specific module within a feature
     */
    public boolean canUserAccessModule(Long userId, String featureId, String moduleName) {
        return featureAccessControlService.canUserAccessModule(userId, featureId, moduleName);
    }
    
    /**
     * Get tenant-specific module configuration for a feature
     */
    public Map<String, Object> getTenantModuleConfig(Long tenantId, String featureId) {
        return tenantModuleConfigService.getTenantModuleConfig(tenantId, featureId);
    }
    
    /**
     * Get custom fields for a module specific to a tenant
     */
    public List<Map<String, Object>> getTenantModuleCustomFields(Long tenantId, String featureId, String moduleName) {
        return tenantModuleConfigService.getCustomFieldsForModule(tenantId, featureId, moduleName);
    }
    
    /**
     * Get a filtered module configuration based on user permissions and tenant settings
     */
    public Map<String, Object> getFilteredModuleConfig(Long userId, Long tenantId, String featureId) {
        // Check if user has access to the feature
        if (!featureAccessControlService.hasUserAccessToFeature(userId, featureId)) {
            return Map.of("error", "Access denied to feature: " + featureId);
        }
        
        // Get base configuration path
        String baseConfigPath = tenantModuleConfigService.getBaseConfigFile(tenantId, featureId);
        
        // Get merged configuration with tenant customizations
        Map<String, Object> config = tenantModuleConfigService.getMergedModuleConfig(tenantId, featureId, baseConfigPath);
        
        // Filter modules based on user permissions
        if (config.containsKey("modules")) {
            List<Map<String, Object>> modules = (List<Map<String, Object>>) config.get("modules");
            List<String> accessibleModules = getUserAccessibleModules(userId, featureId);
            
            modules.removeIf(module -> {
                String moduleName = (String) module.get("moduleName");
                return !accessibleModules.contains(moduleName);
            });
        }
        
        // Add user permissions to the configuration
        List<PermissionAction> userPermissions = getUserFeaturePermissions(userId, featureId);
        config.put("userPermissions", userPermissions.stream()
                .map(PermissionAction::name)
                .collect(Collectors.toList()));
        
        return config;
    }
    
    /**
     * Validate if a tenant-feature-role combination is valid
     */
    public ValidationResult validateTenantFeatureRoleAccess(Long tenantId, String featureId, RoleName roleName) {
        ValidationResult result = new ValidationResult();
        
        // Check if tenant has access to the feature
        if (!tenantFeatureService.hasTenantAccessToFeature(tenantId, featureId)) {
            result.addError("Tenant " + tenantId + " does not have access to feature: " + featureId);
        }
        
        // Check if role has permissions for the feature
        if (roleFeaturePermissionService.getRoleFeaturePermission(roleName, featureId).isEmpty()) {
            result.addError("Role " + roleName + " does not have permissions for feature: " + featureId);
        }
        
        return result;
    }
    
    /**
     * Get summary of tenant's feature access
     */
    public TenantFeatureSummary getTenantFeatureSummary(Long tenantId) {
        TenantFeatureSummary summary = new TenantFeatureSummary();
        summary.setTenantId(tenantId);
        
        // Get all features assigned to tenant
        List<String> assignedFeatures = tenantFeatureService.getTenantFeatures(tenantId);
        summary.setAssignedFeatures(assignedFeatures);
        
        // Get enabled modules for each feature
        Map<String, List<String>> featureModules = assignedFeatures.stream()
                .collect(Collectors.toMap(
                    featureId -> featureId,
                    featureId -> tenantModuleConfigService.getEnabledModulesForTenant(tenantId, featureId)
                ));
        summary.setFeatureModules(featureModules);
        
        return summary;
    }
    
    /**
     * Validation result class
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new java.util.ArrayList<>();
        
        public void addError(String error) {
            this.valid = false;
            this.errors.add(error);
        }
        
        public boolean isValid() { return valid; }
        public List<String> getErrors() { return errors; }
    }
    
    /**
     * Tenant feature summary class
     */
    public static class TenantFeatureSummary {
        private Long tenantId;
        private List<String> assignedFeatures;
        private Map<String, List<String>> featureModules;
        
        // Getters and setters
        public Long getTenantId() { return tenantId; }
        public void setTenantId(Long tenantId) { this.tenantId = tenantId; }
        
        public List<String> getAssignedFeatures() { return assignedFeatures; }
        public void setAssignedFeatures(List<String> assignedFeatures) { this.assignedFeatures = assignedFeatures; }
        
        public Map<String, List<String>> getFeatureModules() { return featureModules; }
        public void setFeatureModules(Map<String, List<String>> featureModules) { this.featureModules = featureModules; }
    }
}
