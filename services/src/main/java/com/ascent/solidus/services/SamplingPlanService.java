package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.SamplingPlanRepository;
import com.ascent.solidus.core.domain.module.SamplingPlan;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class SamplingPlanService {
  private final SamplingPlanRepository repository;

  public SamplingPlanService(SamplingPlanRepository repository) {
    this.repository = repository;
  }

  public Page<SamplingPlan> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<SamplingPlan> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public SamplingPlan findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public SamplingPlan save(SamplingPlan entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
