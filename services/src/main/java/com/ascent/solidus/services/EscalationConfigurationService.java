package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.EscalationConfigurationRepository;
import com.ascent.solidus.core.domain.module.EscalationConfiguration;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class EscalationConfigurationService {
  private final EscalationConfigurationRepository repository;

  public EscalationConfigurationService(EscalationConfigurationRepository repository) {
    this.repository = repository;
  }

  public Page<EscalationConfiguration> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<EscalationConfiguration> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public EscalationConfiguration findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public EscalationConfiguration save(EscalationConfiguration entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
