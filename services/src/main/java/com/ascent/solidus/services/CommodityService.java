package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CommodityRepository;
import com.ascent.solidus.core.domain.module.Commodity;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CommodityService {
  private final CommodityRepository repository;

  public CommodityService(CommodityRepository repository) {
    this.repository = repository;
  }

  public Page<Commodity> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Commodity> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Commodity findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Commodity save(Commodity entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
