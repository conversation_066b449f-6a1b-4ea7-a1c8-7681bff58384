package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.EscalationEmailAudienceRepository;
import com.ascent.solidus.core.domain.module.EscalationEmailAudience;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class EscalationEmailAudienceService {
  private final EscalationEmailAudienceRepository repository;

  public EscalationEmailAudienceService(EscalationEmailAudienceRepository repository) {
    this.repository = repository;
  }

  public Page<EscalationEmailAudience> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<EscalationEmailAudience> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public EscalationEmailAudience findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public EscalationEmailAudience save(EscalationEmailAudience entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
