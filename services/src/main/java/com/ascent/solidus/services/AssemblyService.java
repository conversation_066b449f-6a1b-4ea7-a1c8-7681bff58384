package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssemblyRepository;
import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssemblyService {
  private final AssemblyRepository repository;

  public AssemblyService(AssemblyRepository repository) {
    this.repository = repository;
  }

  public Page<Assembly> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Assembly> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Assembly findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Assembly save(Assembly entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public Product getProductByAssemblyId(String id) {
    return repository.getProductByAssemblyId(id);
  }

  public Revision getRevisionByAssemblyId(String id) {
    return repository.getRevisionByAssemblyId(id);
  }

  public Assembly getAssemblyByAssemblyId(String id) {
    return repository.getAssemblyByAssemblyId(id);
  }

  public List<Assembly> findProductByAssemblyId(String assemblyId) {
    return repository.findProductByAssemblyId(assemblyId);
  }

  public List<Assembly> findRevisionByAssemblyId(String assemblyId) {
    return repository.findRevisionByAssemblyId(assemblyId);
  }
}
