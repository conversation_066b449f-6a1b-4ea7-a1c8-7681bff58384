package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AuditAreaRepository;
import com.ascent.solidus.core.domain.module.AuditArea;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AuditAreaService {
  private final AuditAreaRepository repository;

  public AuditAreaService(AuditAreaRepository repository) {
    this.repository = repository;
  }

  public Page<AuditArea> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AuditArea> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AuditArea findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AuditArea save(AuditArea entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
