package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NonConformanceRepository;
import com.ascent.solidus.core.domain.module.NonConformance;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NonConformanceService {
  private final NonConformanceRepository repository;

  public NonConformanceService(NonConformanceRepository repository) {
    this.repository = repository;
  }

  public Page<NonConformance> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<NonConformance> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NonConformance findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NonConformance save(NonConformance entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
