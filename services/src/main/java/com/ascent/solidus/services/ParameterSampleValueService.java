package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ParameterSampleValueRepository;
import com.ascent.solidus.core.domain.module.ParameterSampleValue;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ParameterSampleValueService {
  private final ParameterSampleValueRepository repository;

  public ParameterSampleValueService(ParameterSampleValueRepository repository) {
    this.repository = repository;
  }

  public Page<ParameterSampleValue> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ParameterSampleValue> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ParameterSampleValue findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ParameterSampleValue save(ParameterSampleValue entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
