package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.MaterialRepository;
import com.ascent.solidus.core.domain.module.Material;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class MaterialService {
  private final MaterialRepository repository;

  public MaterialService(MaterialRepository repository) {
    this.repository = repository;
  }

  public Page<Material> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Material> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Material findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Material save(Material entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
