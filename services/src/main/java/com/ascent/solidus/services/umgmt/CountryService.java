package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.repository.umgmt.CountryRepository;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.services.exceptions.InvalidArgumentException;
import com.ascent.solidus.services.exceptions.ErrorCode;
import com.ascent.solidus.services.tenant.TenantService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class CountryService {

    @Autowired
    CountryRepository countryRepository;

    @Autowired
    TenantService tenantService;

    @Transactional(readOnly = true)
    public List<Country> findAll() {
        return countryRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Country findById(Long id) {
        return countryRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Country not found with id: " + id));
    }

    @Transactional(readOnly = false)
    @PreAuthorize("isFullyAuthenticated()")
    public Country save(Country country) {
        Assert.notNull(country, "'country' is required.");

        // Check if country with same name already exists
        Optional<Country> existingCountry = findByName(country.getName());
        if (existingCountry.isPresent()) {
            throw new InvalidArgumentException("Country with name '" + country.getName() + "' already exists");
        }

        // Set default values
        country.setActive(true);
        country.setCreatedOn(new Date());

        // Set tenant from current context
        Tenant currentTenant = tenantService.getCurrentTenant();
        country.setTenant(currentTenant);

        return countryRepository.save(country);
    }

    @Transactional(readOnly = true)
    public Optional<Country> findByName(String name) {
        return countryRepository.findByName(name);
    }

    @Transactional
    @PreAuthorize("isFullyAuthenticated()")
    public Country update(Country country) {
        Assert.notNull(country, "'country' is required.");
        Assert.notNull(country.getId(), "'country.id' is required.");

        // Ensure the country exists
        Country existingCountry = findById(country.getId());

        // Check if another country with the same name exists
        Optional<Country> countryWithSameName = findByName(country.getName());
        if (countryWithSameName.isPresent() && !countryWithSameName.get().getId().equals(country.getId())) {
            throw new InvalidArgumentException("Another country with name '" + country.getName() + "' already exists");
        }

        // Preserve original values
        country.setCreatedOn(existingCountry.getCreatedOn());
        country.setTenant(existingCountry.getTenant());
        country.setUpdatedOn(new Date());

        return countryRepository.save(country);
    }

    @Transactional
    @PreAuthorize("isFullyAuthenticated()")
    public void delete(Long id) {
        // Ensure the country exists
        Country country = findById(id);

        // Soft delete by setting active to false
        country.setActive(false);
        country.setUpdatedOn(new Date());
        countryRepository.save(country);
    }
}