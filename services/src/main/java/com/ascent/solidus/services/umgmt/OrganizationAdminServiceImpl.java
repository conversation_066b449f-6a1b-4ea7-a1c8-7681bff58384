package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.umgmt.Role;
import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import com.ascent.solidus.core.repository.tenant.OrganizationRepository;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import com.ascent.solidus.core.repository.umgmt.RoleRepository;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.services.tenant.TenantService;
import jakarta.persistence.EntityNotFoundException;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

/**
 * Implementation of the OrganizationAdminService interface.
 * Provides functionality for managing organization administrators.
 */
@Service
public class OrganizationAdminServiceImpl implements OrganizationAdminService {

    @Autowired
    AppUserRepository appUserRepository;

    @Autowired
    OrganizationRepository organizationRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    TenantService tenantService;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public ListResponse findAll(SearchCriteria searchCriteria, AppUser loggedInUser) {
//        log.debug("Finding all organization admins with criteria: {}", searchCriteria);
        Assert.notNull(searchCriteria, "'searchCriteria' is required.");
        Assert.notNull(loggedInUser, "'loggedInUser' is required.");

        // Implement a simple search if the repository method is not available
        // This is a placeholder - you'll need to implement the actual search logic
        ListResponse response = new ListResponse();
        List<AppUser> users = appUserRepository.findAll();
        response.setData(users);
        response.setRecordsTotal((long) users.size());
        response.setRecordsFiltered((long) users.size());

        return response;
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public AppUser save(AppUser appUser) {
//        log.debug("Creating new organization admin: {}", appUser.getEmail());
        Assert.notNull(appUser, "'appUser' is required.");

        // Check if email already exists
        Optional<AppUser> existingByEmail = appUserRepository.findByEmailAndActive(appUser.getEmail(), true);
        if (existingByEmail.isPresent()) {
//            log.warn("Email already exists: {}", appUser.getEmail());
            throw new IllegalArgumentException("User with this email already exists");
        }

        // Get organization
        Organization organization = organizationRepository.findByIdAndActive(appUser.getOrganization().getId(), true)
                .orElseThrow(() -> {
//                    log.error("Organization not found with ID: {}", appUser.getOrganization().getId());
                    return new EntityNotFoundException("Organization not found");
                });

        // Set user properties
        appUser.setUsername(appUser.getEmail());
        appUser.setCreatedOn(new Date());
        appUser.setActive(true);
        appUser.setEnabled(true);

        // Generate and set temporary password
        String tempPassword = RandomStringUtils.randomAlphanumeric(10);
        appUser.setPassword(passwordEncoder.encode(tempPassword));

        // Set organization and tenant
        appUser.setOrganization(organization);
        appUser.setTenant(organization.getTenant());

        // Assign admin role
        Role adminRole = roleRepository.findByName(RoleName.ROLE_ORGANIZATION_ADMIN)
                .orElseThrow(() -> new EntityNotFoundException("Admin role not found"));

        // Initialize assignedRoles if null
        if (appUser.getAssignedRoles() == null) {
            appUser.setAssignedRoles(new HashSet<>());
        }

        RoleAssignment roleAssignment = new RoleAssignment();
        roleAssignment.setAppUser(appUser);
        roleAssignment.setRole(adminRole);
        roleAssignment.setTenant(organization.getTenant());
        roleAssignment.setActive(true);
        roleAssignment.setCreatedOn(new Date());

        appUser.getAssignedRoles().add(roleAssignment);

        // Save user
        AppUser saved = appUserRepository.save(appUser);
//        log.info("Created organization admin with ID: {}", saved.getId());
//
//        // Email sending would go here, but we'll skip it for now
//        log.info("Would send registration email to: {} with temp password: {}", appUser.getEmail(), tempPassword);

        return saved;
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public AppUser update(AppUser appUser) {
//        log.debug("Updating organization admin with ID: {}", appUser.getId());
        Assert.notNull(appUser, "'appUser' is required.");

        // Get existing user
        AppUser existing = appUserRepository.findById(appUser.getId())
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", appUser.getId());
                    return new EntityNotFoundException("User not found");
                });

        // Check if email already exists for another user
        Optional<AppUser> existingByEmail = appUserRepository.findByEmailAndActive(appUser.getEmail(), true);
        if (existingByEmail.isPresent() && !existingByEmail.get().getId().equals(appUser.getId())) {
//            log.warn("Email already exists for another user: {}", appUser.getEmail());
            throw new IllegalArgumentException("Email already exists for another user");
        }

        // Update user properties
        existing.setFirstName(appUser.getFirstName());
//        existing.setMiddleName(appUser.getMiddleName());
        existing.setLastName(appUser.getLastName());
//        existing.setGender(appUser.getGender());
        existing.setPhone(appUser.getPhone());

        // Save updated user
        AppUser updated = appUserRepository.save(existing);
//        log.info("Updated organization admin with ID: {}", updated.getId());

        return updated;
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public AppUser getOne(Long id) {
//        log.debug("Getting organization admin with ID: {}", id);
        Assert.notNull(id, "'id' is required.");

        // Get existing user
        AppUser existing = appUserRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", id);
                    return new EntityNotFoundException("User not found");
                });

        // Get roles
        List<Role> roles = new ArrayList<>();
        if (existing.getAssignedRoles() != null && !existing.getAssignedRoles().isEmpty()) {
            for (RoleAssignment roleAssignment : existing.getAssignedRoles()) {
                roles.add(roleAssignment.getRole());
            }
        }

        // Set the roles list on the user
        existing.setRoles(roles);

        return existing;
    }

    /**
     * Deactivates an organization admin.
     *
     * @param id The ID of the organization admin to deactivate
     * @return The deactivated organization admin
     */
    @Transactional
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public AppUser deactivate(Long id) {
//        log.debug("Deactivating organization admin with ID: {}", id);
        Assert.notNull(id, "'id' is required.");

        // Get existing user
        AppUser existing = appUserRepository.findById(id)
                .orElseThrow(() -> {
//                    log.error("User not found with ID: {}", id);
                    return new EntityNotFoundException("User not found");
                });

        // Deactivate user
        existing.setActive(false);
        existing.setEnabled(false);

        // Save deactivated user
        AppUser deactivated = appUserRepository.save(existing);
//        log.info("Deactivated organization admin with ID: {}", deactivated.getId());

        return deactivated;
    }
}
