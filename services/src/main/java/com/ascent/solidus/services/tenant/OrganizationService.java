package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantMapping;
import com.ascent.solidus.core.domain.tenant.TenantType;
import com.ascent.solidus.core.repository.tenant.OrganizationRepository;
import com.ascent.solidus.core.repository.tenant.TenantMappingRepository;
import com.ascent.solidus.core.repository.tenant.TenantRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class OrganizationService {

    @Autowired
    OrganizationRepository organizationRepository;

    @Autowired
    TenantRepository tenantRepository;

    @Autowired
    TenantMappingRepository tenantMappingRepository;

    @Autowired
    TenantService tenantService;

    @Transactional(readOnly = true)
    public Organization findById(Long id) {
        return organizationRepository.findByIdAndActive(id, true)
                .orElseThrow(() -> new EntityNotFoundException("Organization not found with id: " + id));
    }

    @Transactional(readOnly = true)
    public List<Organization> findByTenant(Long tenantId) {
        Tenant tenant = tenantService.findById(tenantId);
        return organizationRepository.findByTenantAndActive(tenant, true);
    }

    @Transactional
    public Organization createOrganization(Organization organization, Long parentTenantId) {
        // Create a new tenant for this organization
        Tenant newTenant = new Tenant();
        newTenant.setTenantType(TenantType.ROLE_ORGANIZATION_ADMIN);
        newTenant.setActive(true);
        newTenant.setCreatedOn(new Date());
        Tenant savedTenant = tenantRepository.save(newTenant);

        // Create tenant mapping with parent
        Tenant parentTenant = tenantService.findById(parentTenantId);
        TenantMapping tenantMapping = new TenantMapping();
        tenantMapping.setParent(parentTenant);
        tenantMapping.setChild(savedTenant);
        tenantMapping.setActive(true);
        tenantMapping.setCreatedOn(new Date());
        tenantMappingRepository.save(tenantMapping);

        // Set the organization's tenant
        organization.setTenant(savedTenant);
        organization.setActive(true);
        organization.setCreatedOn(new Date());

        // Save the organization
        Organization savedOrganization = organizationRepository.save(organization);

        return savedOrganization;
    }

    @Transactional
    public Organization updateOrganization(Long id, Organization organizationDetails) {
        Organization organization = findById(id);

        organization.setName(organizationDetails.getName());
        organization.setEmail(organizationDetails.getEmail());
        organization.setPhone(organizationDetails.getPhone());
        organization.setPhone(organizationDetails.getPhone());
        organization.setAddressLine1(organization.getAddressLine1());
        organization.setAddressLine2(organization.getAddressLine2());
        organization.setArea(organization.getArea());
        organization.setCity(organization.getCity());
        organization.setContactPersonName(organization.getContactPersonName());
        organization.setCountry(organization.getCountry());
        organization.setZipCode(organization.getZipCode());
        organization.setOtherContactNumber(organization.getOtherContactNumber());
        organization.setState(organization.getState());
        organization.setThemeColor(organization.getThemeColor());
        organization.setLogoPath(organization.getLogoPath());
        organization.setEnvironmentType(organization.getEnvironmentType());

        return organizationRepository.save(organization);
    }

    @Transactional
    public void deactivateOrganization(Long id) {
        Organization organization = findById(id);
        organization.setActive(false);
        organizationRepository.save(organization);

        // Also deactivate the organization's tenant
        Tenant tenant = organization.getTenant();
        tenant.setActive(false);
        tenantRepository.save(tenant);
    }
}