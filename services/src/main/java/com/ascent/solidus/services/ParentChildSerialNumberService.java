package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ParentChildSerialNumberRepository;
import com.ascent.solidus.core.domain.module.ParentChildSerialNumber;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ParentChildSerialNumberService {
  private final ParentChildSerialNumberRepository repository;

  public ParentChildSerialNumberService(ParentChildSerialNumberRepository repository) {
    this.repository = repository;
  }

  public Page<ParentChildSerialNumber> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ParentChildSerialNumber> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ParentChildSerialNumber findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ParentChildSerialNumber save(ParentChildSerialNumber entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
