package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CheckListRevisionHistoryRepository;
import com.ascent.solidus.core.domain.module.CheckListRevisionHistory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CheckListRevisionHistoryService {
  private final CheckListRevisionHistoryRepository repository;

  public CheckListRevisionHistoryService(CheckListRevisionHistoryRepository repository) {
    this.repository = repository;
  }

  public Page<CheckListRevisionHistory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<CheckListRevisionHistory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public CheckListRevisionHistory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public CheckListRevisionHistory save(CheckListRevisionHistory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
