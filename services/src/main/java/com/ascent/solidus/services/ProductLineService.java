package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProductLineRepository;
import com.ascent.solidus.core.domain.module.ProductLine;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProductLineService {
  private final ProductLineRepository repository;

  public ProductLineService(ProductLineRepository repository) {
    this.repository = repository;
  }

  public Page<ProductLine> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProductLine> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProductLine findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProductLine save(ProductLine entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
