package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.DynamicEntityRegistry;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.ascent.solidus.core.domain.module.ModuleConfigRoot;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Component
public class ModuleConfigLoader {
    private final DynamicEntityRegistry entityRegistry;
    private final ObjectMapper objectMapper;

    @Autowired
    public ModuleConfigLoader(DynamicEntityRegistry entityRegistry,
                              ObjectMapper objectMapper) {
        this.entityRegistry = entityRegistry;
        this.objectMapper = objectMapper;
    }

//    @PostConstruct
    public void loadModuleConfigurations() throws IOException, ClassNotFoundException, InterruptedException {
        // Load from JSON file or external source
        String jsonConfig = loadJsonConfig();
        ModuleConfigRoot configRoot = objectMapper.readValue(jsonConfig, ModuleConfigRoot.class);

        // Register all modules
//        configRoot.getModules().forEach((moduleName, moduleConfig) -> {
//            entityRegistry.registerEntity(moduleConfig);
//
//            // Register fields at parent level if they exist
//            if (configRoot.getFields() != null) {
//                moduleConfig.getFields().addAll(configRoot.getFields());
//            }
//        });
        for (ModuleConfig moduleConfig : configRoot.getModules()) {
            entityRegistry.registerEntity(moduleConfig);
            if (configRoot.getFields() != null) {
                moduleConfig.getFields().addAll(configRoot.getFields());
            }
        }
    }

    private String loadJsonConfig() throws IOException {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("module-config.json")) {
            if (is == null) {
                throw new FileNotFoundException("module-config.json not found in classpath");
            }
            return new String(is.readAllBytes(), StandardCharsets.UTF_8);
        }
    }

}
