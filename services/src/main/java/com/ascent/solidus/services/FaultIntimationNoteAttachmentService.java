package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationNoteAttachmentRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationNoteAttachment;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationNoteAttachmentService {
  private final FaultIntimationNoteAttachmentRepository repository;

  public FaultIntimationNoteAttachmentService(FaultIntimationNoteAttachmentRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationNoteAttachment> findAll(Map<String, Object> filters,
      Pageable pageable) {
    Specification<FaultIntimationNoteAttachment> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationNoteAttachment findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationNoteAttachment save(FaultIntimationNoteAttachment entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
