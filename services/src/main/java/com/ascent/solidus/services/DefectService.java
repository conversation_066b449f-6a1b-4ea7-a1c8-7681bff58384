package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DefectRepository;
import com.ascent.solidus.core.domain.module.Defect;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DefectService {
  private final DefectRepository repository;

  public DefectService(DefectRepository repository) {
    this.repository = repository;
  }

  public Page<Defect> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Defect> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Defect findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Defect save(Defect entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
