package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignAssemblyInspectorRepository;
import com.ascent.solidus.core.domain.module.AssignAssemblyInspector;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignAssemblyInspectorService {
  private final AssignAssemblyInspectorRepository repository;

  public AssignAssemblyInspectorService(AssignAssemblyInspectorRepository repository) {
    this.repository = repository;
  }

  public Page<AssignAssemblyInspector> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignAssemblyInspector> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignAssemblyInspector findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignAssemblyInspector save(AssignAssemblyInspector entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
