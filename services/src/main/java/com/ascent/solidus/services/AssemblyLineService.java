package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssemblyLineRepository;
import com.ascent.solidus.core.domain.module.AssemblyLine;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssemblyLineService {
  private final AssemblyLineRepository repository;

  public AssemblyLineService(AssemblyLineRepository repository) {
    this.repository = repository;
  }

  public Page<AssemblyLine> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssemblyLine> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssemblyLine findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssemblyLine save(AssemblyLine entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
