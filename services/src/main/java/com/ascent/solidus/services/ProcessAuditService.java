package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProcessAuditRepository;
import com.ascent.solidus.core.domain.module.ProcessAudit;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProcessAuditService {
  private final ProcessAuditRepository repository;

  public ProcessAuditService(ProcessAuditRepository repository) {
    this.repository = repository;
  }

  public Page<ProcessAudit> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProcessAudit> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProcessAudit findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProcessAudit save(ProcessAudit entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
