package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignAssemblyRepository;
import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.AssemblyInspectionRound;
import com.ascent.solidus.core.domain.module.AssignAssembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Stage;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignAssemblyService {
  private final AssignAssemblyRepository repository;

  public AssignAssemblyService(AssignAssemblyRepository repository) {
    this.repository = repository;
  }

  public Page<AssignAssembly> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignAssembly> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignAssembly findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignAssembly save(AssignAssembly entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public Assembly getAssemblyByAssignAssemblyId(String id) {
    return repository.getAssemblyByAssignAssemblyId(id);
  }

  public Product getProductByAssignAssemblyId(String id) {
    return repository.getProductByAssignAssemblyId(id);
  }

  public AssemblyInspectionRound getAssemblyInspectionRoundByAssignAssemblyId(String id) {
    return repository.getAssemblyInspectionRoundByAssignAssemblyId(id);
  }

  public Stage getStageByAssignAssemblyId(String id) {
    return repository.getStageByAssignAssemblyId(id);
  }

  public List<AssignAssembly> findAssemblyByAssignAssemblyId(String assignassemblyId) {
    return repository.findAssemblyByAssignAssemblyId(assignassemblyId);
  }

  public List<AssignAssembly> findProductByAssignAssemblyId(String assignassemblyId) {
    return repository.findProductByAssignAssemblyId(assignassemblyId);
  }

  public List<AssignAssembly> findStageByAssignAssemblyId(String assignassemblyId) {
    return repository.findStageByAssignAssemblyId(assignassemblyId);
  }

  public List<AssignAssembly> findAssemblyInspectionRoundByAssignAssemblyId(
      String assignassemblyId) {
    return repository.findAssemblyInspectionRoundByAssignAssemblyId(assignassemblyId);
  }
}
