package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProcessAuditCategoryRepository;
import com.ascent.solidus.core.domain.module.ProcessAuditCategory;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProcessAuditCategoryService {
  private final ProcessAuditCategoryRepository repository;

  public ProcessAuditCategoryService(ProcessAuditCategoryRepository repository) {
    this.repository = repository;
  }

  public Page<ProcessAuditCategory> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProcessAuditCategory> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProcessAuditCategory findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProcessAuditCategory save(ProcessAuditCategory entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
