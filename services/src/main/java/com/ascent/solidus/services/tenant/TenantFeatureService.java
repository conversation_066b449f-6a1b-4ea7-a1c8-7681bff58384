package com.ascent.solidus.services.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for managing tenant-feature assignments
 * Reads from tenant-features.json to determine which tenants have access to which features
 */
@Service
public class TenantFeatureService {
    
    private static final Logger logger = LoggerFactory.getLogger(TenantFeatureService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Check if a tenant has access to a specific feature
     */
    public boolean hasTenantAccessToFeature(Long tenantId, String featureId) {
        try {
            JsonNode tenantFeatures = loadTenantFeatures();
            String tenantKey = "tenant_" + tenantId;
            
            if (tenantFeatures.has(tenantKey)) {
                JsonNode tenant = tenantFeatures.get(tenantKey);
                JsonNode features = tenant.get("features");
                
                if (features != null && features.has(featureId)) {
                    JsonNode feature = features.get(featureId);
                    boolean enabled = feature.get("enabled").asBoolean();
                    
                    if (!enabled) {
                        return false;
                    }
                    
                    // Check expiry date if present
                    JsonNode expiryNode = feature.get("expiry_date");
                    if (expiryNode != null && !expiryNode.isNull()) {
                        String expiryDateStr = expiryNode.asText();
                        LocalDateTime expiryDate = LocalDateTime.parse(expiryDateStr, DateTimeFormatter.ISO_DATE_TIME);
                        return LocalDateTime.now().isBefore(expiryDate);
                    }
                    
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("Error checking tenant feature access for tenant {} and feature {}: {}", 
                        tenantId, featureId, e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Get all features assigned to a tenant
     */
    public List<String> getTenantFeatures(Long tenantId) {
        List<String> features = new ArrayList<>();
        
        try {
            JsonNode tenantFeatures = loadTenantFeatures();
            String tenantKey = "tenant_" + tenantId;
            
            if (tenantFeatures.has(tenantKey)) {
                JsonNode tenant = tenantFeatures.get(tenantKey);
                JsonNode featuresNode = tenant.get("features");
                
                if (featuresNode != null) {
                    featuresNode.fieldNames().forEachRemaining(featureId -> {
                        JsonNode feature = featuresNode.get(featureId);
                        boolean enabled = feature.get("enabled").asBoolean();
                        
                        if (enabled) {
                            // Check expiry date if present
                            JsonNode expiryNode = feature.get("expiry_date");
                            if (expiryNode == null || expiryNode.isNull()) {
                                features.add(featureId);
                            } else {
                                try {
                                    String expiryDateStr = expiryNode.asText();
                                    LocalDateTime expiryDate = LocalDateTime.parse(expiryDateStr, DateTimeFormatter.ISO_DATE_TIME);
                                    if (LocalDateTime.now().isBefore(expiryDate)) {
                                        features.add(featureId);
                                    }
                                } catch (Exception e) {
                                    logger.warn("Error parsing expiry date for feature {}: {}", featureId, e.getMessage());
                                    // If we can't parse the date, include the feature
                                    features.add(featureId);
                                }
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            logger.error("Error getting tenant features for tenant {}: {}", tenantId, e.getMessage());
        }
        
        return features;
    }
    
    /**
     * Get all available features (from feature definitions)
     */
    public List<String> getAllAvailableFeatures() {
        List<String> features = new ArrayList<>();
        
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode featureDefinitions = rootNode.get("feature_definitions");
            
            if (featureDefinitions != null) {
                featureDefinitions.fieldNames().forEachRemaining(features::add);
            }
        } catch (Exception e) {
            logger.error("Error getting available features: {}", e.getMessage());
        }
        
        return features;
    }
    
    /**
     * Get feature definition
     */
    public FeatureDefinition getFeatureDefinition(String featureId) {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode featureDefinitions = rootNode.get("feature_definitions");
            
            if (featureDefinitions != null && featureDefinitions.has(featureId)) {
                JsonNode feature = featureDefinitions.get(featureId);
                
                FeatureDefinition definition = new FeatureDefinition();
                definition.setFeatureId(featureId);
                definition.setFeatureName(feature.get("feature_name").asText());
                definition.setDescription(feature.get("description").asText());
                
                List<String> modules = new ArrayList<>();
                JsonNode modulesNode = feature.get("modules");
                if (modulesNode != null && modulesNode.isArray()) {
                    modulesNode.forEach(module -> modules.add(module.asText()));
                }
                definition.setModules(modules);
                
                return definition;
            }
        } catch (Exception e) {
            logger.error("Error getting feature definition for {}: {}", featureId, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Get tenant information
     */
    public TenantInfo getTenantInfo(Long tenantId) {
        try {
            JsonNode tenantFeatures = loadTenantFeatures();
            String tenantKey = "tenant_" + tenantId;
            
            if (tenantFeatures.has(tenantKey)) {
                JsonNode tenant = tenantFeatures.get(tenantKey);
                
                TenantInfo info = new TenantInfo();
                info.setTenantId(tenant.get("tenant_id").asLong());
                info.setTenantName(tenant.get("tenant_name").asText());
                info.setAssignedFeatures(getTenantFeatures(tenantId));
                
                return info;
            }
        } catch (Exception e) {
            logger.error("Error getting tenant info for {}: {}", tenantId, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Load tenant features from JSON file
     */
    private JsonNode loadTenantFeatures() throws IOException {
        ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
        JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
        return rootNode.get("tenant_features");
    }
    
    /**
     * Feature definition DTO
     */
    public static class FeatureDefinition {
        private String featureId;
        private String featureName;
        private String description;
        private List<String> modules;
        
        // Getters and setters
        public String getFeatureId() { return featureId; }
        public void setFeatureId(String featureId) { this.featureId = featureId; }
        
        public String getFeatureName() { return featureName; }
        public void setFeatureName(String featureName) { this.featureName = featureName; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<String> getModules() { return modules; }
        public void setModules(List<String> modules) { this.modules = modules; }
    }
    
    /**
     * Tenant info DTO
     */
    public static class TenantInfo {
        private Long tenantId;
        private String tenantName;
        private List<String> assignedFeatures;
        
        // Getters and setters
        public Long getTenantId() { return tenantId; }
        public void setTenantId(Long tenantId) { this.tenantId = tenantId; }
        
        public String getTenantName() { return tenantName; }
        public void setTenantName(String tenantName) { this.tenantName = tenantName; }
        
        public List<String> getAssignedFeatures() { return assignedFeatures; }
        public void setAssignedFeatures(List<String> assignedFeatures) { this.assignedFeatures = assignedFeatures; }
    }
}
