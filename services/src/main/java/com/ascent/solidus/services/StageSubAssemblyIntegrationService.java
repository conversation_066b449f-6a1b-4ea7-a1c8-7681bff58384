package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.StageSubAssemblyIntegrationRepository;
import com.ascent.solidus.core.domain.module.StageSubAssemblyIntegration;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class StageSubAssemblyIntegrationService {
  private final StageSubAssemblyIntegrationRepository repository;

  public StageSubAssemblyIntegrationService(StageSubAssemblyIntegrationRepository repository) {
    this.repository = repository;
  }

  public Page<StageSubAssemblyIntegration> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<StageSubAssemblyIntegration> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public StageSubAssemblyIntegration findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public StageSubAssemblyIntegration save(StageSubAssemblyIntegration entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
