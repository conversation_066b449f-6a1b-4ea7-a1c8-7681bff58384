package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProcessAuditAnswerRepository;
import com.ascent.solidus.core.domain.module.ProcessAuditAnswer;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProcessAuditAnswerService {
  private final ProcessAuditAnswerRepository repository;

  public ProcessAuditAnswerService(ProcessAuditAnswerRepository repository) {
    this.repository = repository;
  }

  public Page<ProcessAuditAnswer> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProcessAuditAnswer> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProcessAuditAnswer findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProcessAuditAnswer save(ProcessAuditAnswer entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
