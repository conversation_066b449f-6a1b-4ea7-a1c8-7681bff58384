package com.ascent.solidus.services.umgmt;

import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.domain.umgmt.State;
import com.ascent.solidus.core.repository.umgmt.CountryRepository;
import com.ascent.solidus.core.repository.umgmt.StateRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class StateService {

    @Autowired
    StateRepository stateRepository;

    @Autowired
    CountryRepository countryRepository;

    @Transactional(readOnly = true)
    public List<State> findAll() {
        return stateRepository.findAll();
    }

    @Transactional(readOnly = true)
    public State findById(Long id) {
        return stateRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("State not found with id: " + id));
    }

    @Transactional(readOnly = true)
    public List<State> findByCountry(Long countryId) {
        return stateRepository.findByCountryId(countryId);
    }

    @Transactional
    public State save(State state) {
        return stateRepository.save(state);
    }

    @Transactional
    public State createState(String name, Long countryId) {
        Country country = countryRepository.findById(countryId)
                .orElseThrow(() -> new EntityNotFoundException("Country not found with id: " + countryId));

        State state = new State();
        state.setName(name);
        state.setCountry(country);

        return stateRepository.save(state);
    }

    @Transactional
    public State updateState(Long id, String name, Long countryId) {
        State state = findById(id);
        state.setName(name);

        if (countryId != null) {
            Country country = countryRepository.findById(countryId)
                    .orElseThrow(() -> new EntityNotFoundException("Country not found with id: " + countryId));
            state.setCountry(country);
        }

        return stateRepository.save(state);
    }

    @Transactional
    public State update(State state) {
        // Ensure the state exists
        if (!stateRepository.existsById(state.getId())) {
            throw new EntityNotFoundException("State not found with id: " + state.getId());
        }
        return stateRepository.save(state);
    }

    @Transactional
    public void delete(Long id) {
        if (!stateRepository.existsById(id)) {
            throw new EntityNotFoundException("State not found with id: " + id);
        }
        stateRepository.deleteById(id);
    }
}
