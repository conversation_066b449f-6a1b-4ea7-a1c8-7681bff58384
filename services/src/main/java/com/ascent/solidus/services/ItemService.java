package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ItemRepository;
import com.ascent.solidus.core.domain.module.Item;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ItemService {
  private final ItemRepository repository;

  public ItemService(ItemRepository repository) {
    this.repository = repository;
  }

  public Page<Item> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Item> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Item findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Item save(Item entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
