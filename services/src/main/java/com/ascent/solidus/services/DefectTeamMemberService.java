package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.DefectTeamMemberRepository;
import com.ascent.solidus.core.domain.module.DefectTeamMember;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class DefectTeamMemberService {
  private final DefectTeamMemberRepository repository;

  public DefectTeamMemberService(DefectTeamMemberRepository repository) {
    this.repository = repository;
  }

  public Page<DefectTeamMember> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<DefectTeamMember> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public DefectTeamMember findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public DefectTeamMember save(DefectTeamMember entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
