package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignAuditAreaInspectorRepository;
import com.ascent.solidus.core.domain.module.AssignAuditAreaInspector;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignAuditAreaInspectorService {
  private final AssignAuditAreaInspectorRepository repository;

  public AssignAuditAreaInspectorService(AssignAuditAreaInspectorRepository repository) {
    this.repository = repository;
  }

  public Page<AssignAuditAreaInspector> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignAuditAreaInspector> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignAuditAreaInspector findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignAuditAreaInspector save(AssignAuditAreaInspector entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
