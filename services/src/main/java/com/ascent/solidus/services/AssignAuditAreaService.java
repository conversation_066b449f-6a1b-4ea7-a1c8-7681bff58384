package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssignAuditAreaRepository;
import com.ascent.solidus.core.domain.module.AssignAuditArea;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssignAuditAreaService {
  private final AssignAuditAreaRepository repository;

  public AssignAuditAreaService(AssignAuditAreaRepository repository) {
    this.repository = repository;
  }

  public Page<AssignAuditArea> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssignAuditArea> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssignAuditArea findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssignAuditArea save(AssignAuditArea entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
