package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.AssemblyInspectionRoundRepository;
import com.ascent.solidus.core.domain.module.AssemblyInspectionRound;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class AssemblyInspectionRoundService {
  private final AssemblyInspectionRoundRepository repository;

  public AssemblyInspectionRoundService(AssemblyInspectionRoundRepository repository) {
    this.repository = repository;
  }

  public Page<AssemblyInspectionRound> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<AssemblyInspectionRound> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public AssemblyInspectionRound findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public AssemblyInspectionRound save(AssemblyInspectionRound entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
