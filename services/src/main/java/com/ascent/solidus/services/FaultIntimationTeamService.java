package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationTeamRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationTeam;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationTeamService {
  private final FaultIntimationTeamRepository repository;

  public FaultIntimationTeamService(FaultIntimationTeamRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationTeam> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationTeam> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationTeam findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationTeam save(FaultIntimationTeam entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
