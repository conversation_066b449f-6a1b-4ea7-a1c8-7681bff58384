package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.NonConformanceNoteAttachmentRepository;
import com.ascent.solidus.core.domain.module.NonConformanceNoteAttachment;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NonConformanceNoteAttachmentService {
  private final NonConformanceNoteAttachmentRepository repository;

  public NonConformanceNoteAttachmentService(NonConformanceNoteAttachmentRepository repository) {
    this.repository = repository;
  }

  public Page<NonConformanceNoteAttachment> findAll(Map<String, Object> filters,
      Pageable pageable) {
    Specification<NonConformanceNoteAttachment> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public NonConformanceNoteAttachment findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public NonConformanceNoteAttachment save(NonConformanceNoteAttachment entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
