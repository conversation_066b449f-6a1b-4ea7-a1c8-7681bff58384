package com.ascent.solidus.services.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for managing tenant-specific module configurations
 */
@Service
public class TenantModuleConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(TenantModuleConfigService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Get tenant-specific module configuration for a feature
     */
    public Map<String, Object> getTenantModuleConfig(Long tenantId, String featureId) {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-module-configs.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode tenantConfigs = rootNode.get("tenant_module_configurations");
            
            if (tenantConfigs != null) {
                String tenantKey = "tenant_" + tenantId;
                JsonNode tenantConfig = tenantConfigs.get(tenantKey);
                
                if (tenantConfig != null) {
                    JsonNode featureMappings = tenantConfig.get("feature_module_mappings");
                    if (featureMappings != null && featureMappings.has(featureId)) {
                        JsonNode featureConfig = featureMappings.get(featureId);
                        return objectMapper.convertValue(featureConfig, Map.class);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("Error loading tenant module configuration: {}", e.getMessage());
        }
        
        return new HashMap<>();
    }
    
    /**
     * Get custom fields for a specific module for a tenant
     */
    public List<Map<String, Object>> getCustomFieldsForModule(Long tenantId, String featureId, String moduleName) {
        Map<String, Object> tenantConfig = getTenantModuleConfig(tenantId, featureId);
        
        if (tenantConfig.containsKey("custom_modules")) {
            Map<String, Object> customModules = (Map<String, Object>) tenantConfig.get("custom_modules");
            if (customModules.containsKey(moduleName)) {
                Map<String, Object> moduleConfig = (Map<String, Object>) customModules.get(moduleName);
                if (moduleConfig.containsKey("custom_fields")) {
                    return (List<Map<String, Object>>) moduleConfig.get("custom_fields");
                }
            }
        }
        
        return new ArrayList<>();
    }
    
    /**
     * Check if a module is enabled for a tenant
     */
    public boolean isModuleEnabledForTenant(Long tenantId, String featureId, String moduleName) {
        Map<String, Object> tenantConfig = getTenantModuleConfig(tenantId, featureId);
        
        if (tenantConfig.containsKey("custom_modules")) {
            Map<String, Object> customModules = (Map<String, Object>) tenantConfig.get("custom_modules");
            if (customModules.containsKey(moduleName)) {
                Map<String, Object> moduleConfig = (Map<String, Object>) customModules.get(moduleName);
                if (moduleConfig.containsKey("enabled")) {
                    return (Boolean) moduleConfig.get("enabled");
                }
            }
        }
        
        // Default to enabled if not specified
        return true;
    }
    
    /**
     * Get all enabled modules for a tenant's feature
     */
    public List<String> getEnabledModulesForTenant(Long tenantId, String featureId) {
        List<String> enabledModules = new ArrayList<>();
        Map<String, Object> tenantConfig = getTenantModuleConfig(tenantId, featureId);
        
        if (tenantConfig.containsKey("custom_modules")) {
            Map<String, Object> customModules = (Map<String, Object>) tenantConfig.get("custom_modules");
            
            for (Map.Entry<String, Object> entry : customModules.entrySet()) {
                String moduleName = entry.getKey();
                Map<String, Object> moduleConfig = (Map<String, Object>) entry.getValue();
                
                boolean enabled = true; // Default to enabled
                if (moduleConfig.containsKey("enabled")) {
                    enabled = (Boolean) moduleConfig.get("enabled");
                }
                
                if (enabled) {
                    enabledModules.add(moduleName);
                }
            }
        }
        
        return enabledModules;
    }
    
    /**
     * Get the base module configuration file path for a feature
     */
    public String getBaseConfigFile(Long tenantId, String featureId) {
        Map<String, Object> tenantConfig = getTenantModuleConfig(tenantId, featureId);
        
        if (tenantConfig.containsKey("config_file")) {
            return (String) tenantConfig.get("config_file");
        }
        
        // Default to feature-based naming
        return featureId.toLowerCase().replace("_", "-") + "-config.json";
    }
    
    /**
     * Merge base module configuration with tenant-specific customizations
     */
    public Map<String, Object> getMergedModuleConfig(Long tenantId, String featureId, String baseConfigPath) {
        Map<String, Object> mergedConfig = new HashMap<>();
        
        try {
            // Load base configuration
            ClassPathResource baseResource = new ClassPathResource(baseConfigPath);
            if (baseResource.exists()) {
                JsonNode baseConfig = objectMapper.readTree(baseResource.getInputStream());
                mergedConfig = objectMapper.convertValue(baseConfig, Map.class);
            }
            
            // Apply tenant-specific customizations
            List<String> enabledModules = getEnabledModulesForTenant(tenantId, featureId);
            Map<String, Object> tenantConfig = getTenantModuleConfig(tenantId, featureId);
            
            // Filter modules based on tenant enablement
            if (mergedConfig.containsKey("modules")) {
                List<Map<String, Object>> modules = (List<Map<String, Object>>) mergedConfig.get("modules");
                modules.removeIf(module -> {
                    String moduleName = (String) module.get("moduleName");
                    return !enabledModules.isEmpty() && !enabledModules.contains(moduleName);
                });
            }
            
            // Add custom fields to modules
            if (tenantConfig.containsKey("custom_modules")) {
                Map<String, Object> customModules = (Map<String, Object>) tenantConfig.get("custom_modules");
                
                if (mergedConfig.containsKey("modules")) {
                    List<Map<String, Object>> modules = (List<Map<String, Object>>) mergedConfig.get("modules");
                    
                    for (Map<String, Object> module : modules) {
                        String moduleName = (String) module.get("moduleName");
                        if (customModules.containsKey(moduleName)) {
                            Map<String, Object> customModule = (Map<String, Object>) customModules.get(moduleName);
                            if (customModule.containsKey("custom_fields")) {
                                List<Map<String, Object>> customFields = (List<Map<String, Object>>) customModule.get("custom_fields");
                                
                                // Add custom fields to existing fields
                                List<Map<String, Object>> existingFields = (List<Map<String, Object>>) module.get("fields");
                                if (existingFields == null) {
                                    existingFields = new ArrayList<>();
                                    module.put("fields", existingFields);
                                }
                                existingFields.addAll(customFields);
                            }
                        }
                    }
                }
            }
            
        } catch (IOException e) {
            logger.error("Error merging module configuration for tenant {}, feature {}: {}", 
                        tenantId, featureId, e.getMessage());
        }
        
        return mergedConfig;
    }
}
