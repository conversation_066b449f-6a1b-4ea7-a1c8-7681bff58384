package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CheckListQuestionRepository;
import com.ascent.solidus.core.domain.module.CheckListQuestion;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CheckListQuestionService {
  private final CheckListQuestionRepository repository;

  public CheckListQuestionService(CheckListQuestionRepository repository) {
    this.repository = repository;
  }

  public Page<CheckListQuestion> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<CheckListQuestion> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public CheckListQuestion findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public CheckListQuestion save(CheckListQuestion entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
