//package com.ascent.solidus.services.security;
//
//import com.ascent.solidus.core.constants.RoleName;
//import com.ascent.solidus.core.domain.module.Module;
//import com.ascent.solidus.core.domain.module.AppUser;
//import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
//import com.ascent.solidus.core.repository.module.ModulePermissionRepository;
//import com.ascent.solidus.core.repository.umgmt.RoleAssignmentRepository;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
//@Service
//@RequiredArgsConstructor
//@Slf4j
//public class ModuleAccessService {
//
//    private final RoleAssignmentRepository roleAssignmentRepository;
//    private final ModulePermissionRepository modulePermissionRepository;
//
//    @Transactional(readOnly = true)
//    public boolean hasAccessToModule(Long userId, Long moduleId) {
//        // Get user's active roles
//        List<RoleAssignment> roleAssignments = roleAssignmentRepository.findAllActiveRolesByUserId(userId);
//
//        // Extract role names
//        List<RoleName> roleNames = roleAssignments.stream()
//                .map(ra -> ra.getRole().getName())
//                .collect(Collectors.toList());
//
//        // Check if any of the user's roles has access to the module
//        for (RoleName roleName : roleNames) {
//            List<Long> accessibleModuleIds = modulePermissionRepository.findModuleIdsByRoleName(roleName);
//            if (accessibleModuleIds.contains(moduleId)) {
//                return true;
//            }
//        }
//
//        return false;
//    }
//
//    @Transactional(readOnly = true)
//    public List<Long> getAccessibleModuleIds(Long userId) {
//        // Get user's active roles
//        List<RoleAssignment> roleAssignments = roleAssignmentRepository.findAllActiveRolesByUserId(userId);
//
//        // Extract role names
//        List<RoleName> roleNames = roleAssignments.stream()
//                .map(ra -> ra.getRole().getName())
//                .collect(Collectors.toList());
//
//        // Get all accessible module IDs for all roles
//        return roleNames.stream()
//                .flatMap(roleName -> modulePermissionRepository.findModuleIdsByRoleName(roleName).stream())
//                .distinct()
//                .collect(Collectors.toList());
//    }
//}