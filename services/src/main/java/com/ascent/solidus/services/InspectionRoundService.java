package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.InspectionRoundRepository;
import com.ascent.solidus.core.domain.module.InspectionRound;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class InspectionRoundService {
  private final InspectionRoundRepository repository;

  public InspectionRoundService(InspectionRoundRepository repository) {
    this.repository = repository;
  }

  public Page<InspectionRound> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<InspectionRound> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public InspectionRound findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public InspectionRound save(InspectionRound entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
