package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CTQParameterRepository;
import com.ascent.solidus.core.domain.module.CTQParameter;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CTQParameterService {
  private final CTQParameterRepository repository;

  public CTQParameterService(CTQParameterRepository repository) {
    this.repository = repository;
  }

  public Page<CTQParameter> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<CTQParameter> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public CTQParameter findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public CTQParameter save(CTQParameter entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
