package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ReportRepository;
import com.ascent.solidus.core.domain.module.Report;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ReportService {
  private final ReportRepository repository;

  public ReportService(ReportRepository repository) {
    this.repository = repository;
  }

  public Page<Report> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<Report> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public Report findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public Report save(Report entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
