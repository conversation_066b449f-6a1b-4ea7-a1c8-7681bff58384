package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProductFamilyRepository;
import com.ascent.solidus.core.domain.module.ProductFamily;
import com.ascent.solidus.core.domain.module.ProductLabel;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProductFamilyService {
  private final ProductFamilyRepository repository;

  public ProductFamilyService(ProductFamilyRepository repository) {
    this.repository = repository;
  }

  public Page<ProductFamily> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProductFamily> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProductFamily findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProductFamily save(ProductFamily entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }

  public ProductLabel getProductLabelByProductFamilyId(String id) {
    return repository.getProductLabelByProductFamilyId(id);
  }
}
