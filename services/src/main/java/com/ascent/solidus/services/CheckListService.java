package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.CheckListRepository;
import com.ascent.solidus.core.domain.module.CheckList;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CheckListService {
  private final CheckListRepository repository;

  public CheckListService(CheckListRepository repository) {
    this.repository = repository;
  }

  public Page<CheckList> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<CheckList> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public CheckList findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public CheckList save(CheckList entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
