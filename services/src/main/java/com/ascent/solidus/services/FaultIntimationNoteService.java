package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationNoteRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationNote;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationNoteService {
  private final FaultIntimationNoteRepository repository;

  public FaultIntimationNoteService(FaultIntimationNoteRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationNote> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationNote> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationNote findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationNote save(FaultIntimationNote entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
