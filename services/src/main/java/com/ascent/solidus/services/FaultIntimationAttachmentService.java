package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.FaultIntimationAttachmentRepository;
import com.ascent.solidus.core.domain.module.FaultIntimationAttachment;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class FaultIntimationAttachmentService {
  private final FaultIntimationAttachmentRepository repository;

  public FaultIntimationAttachmentService(FaultIntimationAttachmentRepository repository) {
    this.repository = repository;
  }

  public Page<FaultIntimationAttachment> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<FaultIntimationAttachment> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public FaultIntimationAttachment findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public FaultIntimationAttachment save(FaultIntimationAttachment entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
