package com.ascent.solidus.services;

import com.ascent.solidus.core.dao.ProductLabelRepository;
import com.ascent.solidus.core.domain.module.ProductLabel;
import com.ascent.solidus.core.util.PredicateBuilder;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProductLabelService {
  private final ProductLabelRepository repository;

  public ProductLabelService(ProductLabelRepository repository) {
    this.repository = repository;
  }

  public Page<ProductLabel> findAll(Map<String, Object> filters, Pageable pageable) {
    Specification<ProductLabel> spec = PredicateBuilder.buildSpecification(filters);
    return repository.findAll(spec, pageable);
  }

  public ProductLabel findById(String id) {
    return repository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
  }

  public ProductLabel save(ProductLabel entity) {
    return repository.save(entity);
  }

  public void delete(String id) {
    repository.deleteById(id);
  }
}
