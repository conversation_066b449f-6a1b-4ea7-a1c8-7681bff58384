package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.DynamicEntityRegistry;
import com.ascent.solidus.core.dao.ParentModuleRepository;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.ascent.solidus.core.domain.module.ParentModule;
import com.ascent.solidus.core.domain.module.ParentModuleDTO;
import com.ascent.solidus.core.domain.module.SubmoduleConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ParentModuleService {

    @Autowired
    private ParentModuleRepository parentModuleRepo;

    @Autowired
    private DynamicEntityRegistry entityRegistry;

    private final Map<String, List<SubmoduleConfig>> submoduleStore = new ConcurrentHashMap<>();


    public ParentModule createParentModule(ParentModuleDTO dto) {
        ParentModule module = new ParentModule();
        module.setModuleId(dto.getModuleId());
        module.setModuleName(dto.getModuleName());
        module.setModuleType("parent");
//        module.setModuleIcon(dto.getModuleIcon());
//        module.setIsComponent(dto.getSubmodules() != null && !dto.getSubmodules().isEmpty());
//        ParentModule saved = parentModuleRepo.save(module);

        // Register submodules
        if (dto.getSubmodules() != null) {
            dto.getSubmodules().forEach(sub -> {
                ModuleConfig config = convertToModuleConfig(sub,module);
                try {
                    entityRegistry.registerEntity(config);
                    module.getModules().add(config);
                } catch (IOException | InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return parentModuleRepo.save(module);
//        return saved;
    }

    public List<SubmoduleConfig> getSubmodules(ParentModule parentId) {
        return submoduleStore.getOrDefault(parentId, Collections.emptyList());
    }

    public SubmoduleConfig addSubmodule(ParentModule parentId, SubmoduleConfig submoduleConfig) throws IOException, ClassNotFoundException, InterruptedException {
        // Validate that parent exists
        ParentModule parent = parentModuleRepo.findById(parentId.getModuleId())
                .orElseThrow(() -> new NoSuchElementException("Parent module not found: " + parentId));

        // Convert SubmoduleConfig → ModuleConfig and register dynamically
        ModuleConfig moduleConfig = convertToModuleConfig(submoduleConfig, parentId);
        entityRegistry.registerEntity(moduleConfig);

        // Store in memory
        submoduleStore.computeIfAbsent(parentId.getModuleId(), k -> new ArrayList<>()).add(submoduleConfig);

        return submoduleConfig;
    }

    private ModuleConfig convertToModuleConfig(SubmoduleConfig sub, ParentModule parentId) {
        ModuleConfig config = new ModuleConfig();
        config.setModuleId(sub.getModuleId());
        config.setModuleName(sub.getModuleName());
        config.setParentModuleId(parentId.getModuleId());
        config.setFields(sub.getFields());
        return config;
    }
}

