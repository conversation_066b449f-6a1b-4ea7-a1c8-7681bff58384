package com.ascent.solidus.services.module;


import com.ascent.solidus.core.dao.DynamicEntityDao;
import com.ascent.solidus.core.dao.DynamicEntityRegistry;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Transactional
public class DynamicEntityServiceImpl implements DynamicEntityService {
    private final DynamicEntityDao entityDao;
    private final DynamicEntityRegistry registry;

    @Autowired
    public DynamicEntityServiceImpl(DynamicEntityDao entityDao,
                                    DynamicEntityRegistry registry) {
        this.entityDao = entityDao;
        this.registry = registry;
    }

    @Override
    public Object create(String entityType, Map<String, Object> fieldValues) {
        validateEntityType(entityType);
        ModuleConfig config = registry.getModuleConfig(entityType);

        // Add parent module reference if this is a submodule
        if (config.getParentModuleId() != null &&
                !config.getParentModuleId().equals(config.getModuleId())) {
            fieldValues.put("parentModuleId", config.getParentModuleId());
        }

        return entityDao.create(entityType, fieldValues);
    }

    @Override
    public Object findById(String entityType, String id) {
        validateEntityType(entityType);
        return entityDao.findById(entityType, id);
    }

    @Override
    public List<Object> findAll(String entityType) {
        validateEntityType(entityType);
        return entityDao.findAll(entityType);
    }

    @Override
    public List<Object> findByParentModule(String entityType, String parentModuleId) throws Exception{
        validateEntityType(entityType);
        return entityDao.findByParentModule(entityType, parentModuleId);
    }

    @Override
    public Object update(String entityType, String id, Map<String, Object> updates) {
        validateEntityType(entityType);
        Object entity = findById(entityType, id);
//        if (entity == null) {
//            throw new EntityOperationException("Not found", "ENTITY_NOT_FOUND");
//        }
        return entityDao.update(entityType, id, updates);
    }

    @Override
    public void delete(String entityType, String id) {
        validateEntityType(entityType);
//        if (findById(entityType, id) == null) {
//            throw new EntityOperationException("Not found", "ENTITY_NOT_FOUND");
//        }
        entityDao.delete(entityType, id);
    }

    @Override
    public boolean isEntityTypeRegistered(String entityType) {
        return registry.isRegistered(entityType);
    }

    private void validateEntityType(String entityType) {
        if (!isEntityTypeRegistered(entityType)) {
//            throw new EntityOperationException(
//                    "Unknown entity type: " + entityType,
//                    "UNKNOWN_ENTITY_TYPE"
//            );
        }
    }
}
