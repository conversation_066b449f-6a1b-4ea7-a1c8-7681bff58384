package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.AssemblyInspectionRound;
import com.ascent.solidus.core.domain.module.AssignAssembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Stage;
import com.ascent.solidus.services.AssignAssemblyService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/assignassembly")
public class AssignAssemblyController {
  private final AssignAssemblyService service;

  public AssignAssemblyController(AssignAssemblyService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public AssignAssembly getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public AssignAssembly create(@RequestBody AssignAssembly entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public AssignAssembly update(@PathVariable("id") String id, @RequestBody AssignAssembly entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }

  @GetMapping("/{id}/assembly")
  public ResponseEntity<Assembly> getAssemblyForAssignAssembly(@PathVariable("id") String id) {
    Assembly entity = service.getAssemblyByAssignAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/product")
  public ResponseEntity<Product> getProductForAssignAssembly(@PathVariable("id") String id) {
    Product entity = service.getProductByAssignAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/assemblyinspectionround")
  public ResponseEntity<AssemblyInspectionRound> getAssemblyInspectionRoundForAssignAssembly(
      @PathVariable("id") String id) {
    AssemblyInspectionRound entity = service.getAssemblyInspectionRoundByAssignAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/stage")
  public ResponseEntity<Stage> getStageForAssignAssembly(@PathVariable("id") String id) {
    Stage entity = service.getStageByAssignAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{assignassemblyId}/referencing-assemblys")
  public ResponseEntity<List<AssignAssembly>> getAssemblysReferencingAssignAssembly(
      @PathVariable("assignassemblyId") String assignassemblyId) {
    return ResponseEntity.ok(service.findAssemblyByAssignAssemblyId(assignassemblyId));
  }

  @GetMapping("/{assignassemblyId}/referencing-products")
  public ResponseEntity<List<AssignAssembly>> getProductsReferencingAssignAssembly(
      @PathVariable("assignassemblyId") String assignassemblyId) {
    return ResponseEntity.ok(service.findProductByAssignAssemblyId(assignassemblyId));
  }

  @GetMapping("/{assignassemblyId}/referencing-stages")
  public ResponseEntity<List<AssignAssembly>> getStagesReferencingAssignAssembly(
      @PathVariable("assignassemblyId") String assignassemblyId) {
    return ResponseEntity.ok(service.findStageByAssignAssemblyId(assignassemblyId));
  }

  @GetMapping("/{assignassemblyId}/referencing-assemblyinspectionrounds")
  public ResponseEntity<List<AssignAssembly>> getAssemblyInspectionRoundsReferencingAssignAssembly(
      @PathVariable("assignassemblyId") String assignassemblyId) {
    return ResponseEntity.ok(service.findAssemblyInspectionRoundByAssignAssemblyId(assignassemblyId));
  }
}
