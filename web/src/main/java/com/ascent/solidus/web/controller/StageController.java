package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Stage;
import com.ascent.solidus.core.domain.module.StageCategory;
import com.ascent.solidus.services.StageService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/stage")
public class StageController {
  private final StageService service;

  public StageController(StageService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public Stage getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public Stage create(@RequestBody Stage entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public Stage update(@PathVariable("id") String id, @RequestBody Stage entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }

  @GetMapping("/{id}/assembly")
  public ResponseEntity<Assembly> getAssemblyForStage(@PathVariable("id") String id) {
    Assembly entity = service.getAssemblyByStageId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/stagecategory")
  public ResponseEntity<StageCategory> getStageCategoryForStage(@PathVariable("id") String id) {
    StageCategory entity = service.getStageCategoryByStageId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{stageId}/referencing-assemblys")
  public ResponseEntity<List<Stage>> getAssemblysReferencingStage(
      @PathVariable("stageId") String stageId) {
    return ResponseEntity.ok(service.findAssemblyByStageId(stageId));
  }
}
