package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.ParameterSample;
import com.ascent.solidus.services.ParameterSampleService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/parametersample")
public class ParameterSampleController {
  private final ParameterSampleService service;

  public ParameterSampleController(ParameterSampleService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public ParameterSample getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public ParameterSample create(@RequestBody ParameterSample entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public ParameterSample update(@PathVariable("id") String id,
      @RequestBody ParameterSample entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }
}
