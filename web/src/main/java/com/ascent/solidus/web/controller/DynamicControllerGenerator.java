//package com.ascent.solidus.web.controller;
//
//import com.squareup.javapoet.*;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.stereotype.Service;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import javax.lang.model.element.Modifier;
//import java.io.File;
//import java.io.IOException;
//
//@Component
//public class DynamicControllerGenerator {
//    private static final String CONTROLLER_PACKAGE = "com.ascent.solidus.core.controller.dynamic";
//    private static final String OUTPUT_DIR = "src/main/java";
//
//    public JavaFile generateControllerClass(Class<?> entityClass) throws IOException {
//        String entityName = entityClass.getSimpleName();
//        String controllerName = entityName + "Controller";
//        String serviceName = entityName + "Service";
//        String servicePackage = "com.ascent.solidus.core.service.dynamic";
//        String basePath = "/api/" + entityName.toLowerCase();
//
//        // Field for service
//        FieldSpec serviceField = FieldSpec.builder(
//                        ClassName.get(servicePackage, serviceName),
//                        "service",
//                        Modifier.PRIVATE, Modifier.FINAL)
//                .build();
//
//        // Constructor
//        MethodSpec constructor = MethodSpec.constructorBuilder()
//                .addModifiers(Modifier.PUBLIC)
//                .addParameter(ClassName.get(servicePackage, serviceName), "service")
//                .addStatement("this.service = service")
//                .build();
//
//        // GET all endpoint
//        MethodSpec getAll = MethodSpec.methodBuilder("getAll")
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(GetMapping.class)
//                .returns(ParameterizedTypeName.get(
//                        ClassName.get("java.util", "List"),
//                        ClassName.get(entityClass)))
//                .addStatement("return service.findAll()")
//                .build();
//
//        // GET by ID endpoint
//        MethodSpec getById = MethodSpec.methodBuilder("getById")
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(AnnotationSpec.builder(GetMapping.class)
//                        .addMember("value", "$S", "/{id}")
//                        .build())
//                .returns(ClassName.get(entityClass))
//                .addParameter(
//                        ParameterSpec.builder(String.class, "id")
//                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
//                                        .addMember("value", "$S", "id")
//                                        .build())
//                                .build())
//                .addStatement("return service.findById(id)")
//                .build();
//
//        // POST create endpoint
//        MethodSpec create = MethodSpec.methodBuilder("create")
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(PostMapping.class)
//                .returns(ClassName.get(entityClass))
//                .addParameter(
//                        ParameterSpec.builder(ClassName.get(entityClass), "entity")
//                                .addAnnotation(RequestBody.class)
//                                .build())
//                .addStatement("return service.save(entity)")
//                .build();
//
//        // PUT update endpoint
//        MethodSpec update = MethodSpec.methodBuilder("update")
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(AnnotationSpec.builder(PutMapping.class)
//                        .addMember("value", "$S", "/{id}")
//                        .build())
//                .returns(ClassName.get(entityClass))
//                .addParameter(
//                        ParameterSpec.builder(String.class, "id")
//                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
//                                        .addMember("value", "$S", "id")
//                                        .build())
//                                .build())
//                .addParameter(
//                        ParameterSpec.builder(ClassName.get(entityClass), "entity")
//                                .addAnnotation(RequestBody.class)
//                                .build())
//                .addStatement("entity.setId(id)")
//                .addStatement("return service.save(entity)")
//                .build();
//
//        // DELETE endpoint
//        MethodSpec delete = MethodSpec.methodBuilder("delete")
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(AnnotationSpec.builder(DeleteMapping.class)
//                        .addMember("value", "$S", "/{id}")
//                        .build())
//                .addParameter(
//                        ParameterSpec.builder(String.class, "id")
//                                .addAnnotation(AnnotationSpec.builder(PathVariable.class)
//                                        .addMember("value", "$S", "id")
//                                        .build())
//                                .build())
//                .addStatement("service.delete(id)")
//                .build();
//
//        // Build the class
//        TypeSpec controllerClass = TypeSpec.classBuilder(controllerName)
//                .addModifiers(Modifier.PUBLIC)
//                .addAnnotation(RestController.class)
//                .addAnnotation(AnnotationSpec.builder(RequestMapping.class)
//                        .addMember("value", "$S", basePath)
//                        .build())
//                .addField(serviceField)
//                .addMethod(constructor)
//                .addMethod(getAll)
//                .addMethod(getById)
//                .addMethod(create)
//                .addMethod(update)
//                .addMethod(delete)
//                .build();
//
//        // Build Java file and write to disk
//        JavaFile javaFile = JavaFile.builder(CONTROLLER_PACKAGE, controllerClass)
//                .build();
//        javaFile.writeTo(new File(OUTPUT_DIR));
//        return javaFile;
//    }
//}