package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import com.ascent.solidus.services.RevisionService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/revision")
public class RevisionController {
  private final RevisionService service;

  public RevisionController(RevisionService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public Revision getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public Revision create(@RequestBody Revision entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public Revision update(@PathVariable("id") String id, @RequestBody Revision entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }

  @GetMapping("/{id}/product")
  public ResponseEntity<Product> getProductForRevision(@PathVariable("id") String id) {
    Product entity = service.getProductByRevisionId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{revisionId}/referencing-products")
  public ResponseEntity<List<Revision>> getProductsReferencingRevision(
      @PathVariable("revisionId") String revisionId) {
    return ResponseEntity.ok(service.findProductByRevisionId(revisionId));
  }
}
