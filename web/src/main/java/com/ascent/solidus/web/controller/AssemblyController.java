package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.Assembly;
import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.Revision;
import com.ascent.solidus.services.AssemblyService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/assembly")
public class AssemblyController {
  private final AssemblyService service;

  public AssemblyController(AssemblyService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public Assembly getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public Assembly create(@RequestBody Assembly entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public Assembly update(@PathVariable("id") String id, @RequestBody Assembly entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }

  @GetMapping("/{id}/product")
  public ResponseEntity<Product> getProductForAssembly(@PathVariable("id") String id) {
    Product entity = service.getProductByAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/revision")
  public ResponseEntity<Revision> getRevisionForAssembly(@PathVariable("id") String id) {
    Revision entity = service.getRevisionByAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{id}/parent")
  public ResponseEntity<Assembly> getAssemblyForAssembly(@PathVariable("id") String id) {
    Assembly entity = service.getAssemblyByAssemblyId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{assemblyId}/referencing-products")
  public ResponseEntity<List<Assembly>> getProductsReferencingAssembly(
      @PathVariable("assemblyId") String assemblyId) {
    return ResponseEntity.ok(service.findProductByAssemblyId(assemblyId));
  }

  @GetMapping("/{assemblyId}/referencing-revisions")
  public ResponseEntity<List<Assembly>> getRevisionsReferencingAssembly(
      @PathVariable("assemblyId") String assemblyId) {
    return ResponseEntity.ok(service.findRevisionByAssemblyId(assemblyId));
  }
}
