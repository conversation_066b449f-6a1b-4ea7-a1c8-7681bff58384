package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.umgmt.City;
import com.ascent.solidus.services.umgmt.CityService;
import com.ascent.solidus.web.dto.CityRequest;
import com.ascent.solidus.web.dto.CityResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/cities")
public class CityController {

    @Autowired
    CityService cityService;

    @GetMapping
    @Transactional(readOnly = true)
    public ResponseEntity<List<CityResponse>> getAllCities() {
        List<City> cities = cityService.findAll();
        List<CityResponse> responses = cities.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    @Transactional(readOnly = true)
    public ResponseEntity<CityResponse> getCity(@PathVariable Long id) {
        City city = cityService.findById(id);
        return ResponseEntity.ok(mapToResponse(city));
    }

    @GetMapping("/state/{stateId}")
    @Transactional(readOnly = true)
    public ResponseEntity<List<CityResponse>> getCitiesByState(@PathVariable Long stateId) {
        List<City> cities = cityService.findByState(stateId);
        List<CityResponse> responses = cities.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(responses);
    }

    @PostMapping
    public ResponseEntity<CityResponse> createCity(@RequestBody CityRequest request) {
        City savedCity = cityService.createCity(request.getName(), request.getStateId());
        return new ResponseEntity<>(mapToResponse(savedCity), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<CityResponse> updateCity(
            @PathVariable Long id,
            @RequestBody CityRequest request) {

        City updatedCity = cityService.updateCity(id, request.getName(), request.getStateId());
        return ResponseEntity.ok(mapToResponse(updatedCity));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCity(@PathVariable Long id) {
        cityService.delete(id);
        return ResponseEntity.noContent().build();
    }

    private CityResponse mapToResponse(City city) {
        CityResponse response = new CityResponse();
        response.setId(city.getId());
        response.setName(city.getName());

        if (city.getState() != null) {
            response.setStateId(city.getState().getId());
            response.setStateName(city.getState().getName());

            if (city.getState().getCountry() != null) {
                response.setCountryId(city.getState().getCountry().getId());
                response.setCountryName(city.getState().getCountry().getName());
            }
        }

        return response;
    }
}
