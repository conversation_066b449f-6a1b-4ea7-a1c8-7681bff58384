package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.umgmt.Country;
import com.ascent.solidus.core.domain.umgmt.State;
import com.ascent.solidus.core.repository.umgmt.CountryRepository;
import com.ascent.solidus.services.umgmt.StateService;
import com.ascent.solidus.web.dto.StateRequest;
import com.ascent.solidus.web.dto.StateResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/states")
public class StateController {

    @Autowired
    StateService stateService;

    @Autowired
    CountryRepository countryRepository;

    @GetMapping
    @Transactional(readOnly = true)
    public ResponseEntity<List<StateResponse>> getAllStates() {
        List<State> states = stateService.findAll();
        List<StateResponse> responses = states.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(responses);
    }

    @GetMapping("/{id}")
    @Transactional(readOnly = true)
    public ResponseEntity<StateResponse> getState(@PathVariable Long id) {
        State state = stateService.findById(id);
        return ResponseEntity.ok(mapToResponse(state));
    }

    @PostMapping
    public ResponseEntity<StateResponse> createState(@RequestBody StateRequest request) {
        State savedState = stateService.createState(request.getName(), request.getCountryId());
        return new ResponseEntity<>(mapToResponse(savedState), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Transactional
    public ResponseEntity<StateResponse> updateState(
            @PathVariable Long id,
            @RequestBody StateRequest request) {

        State state = stateService.findById(id);
        state.setName(request.getName());

        if (request.getCountryId() != null) {
            Country country = countryRepository.findById(request.getCountryId())
                    .orElseThrow(() -> new RuntimeException("Country not found"));
            state.setCountry(country);
        }

        State updatedState = stateService.update(state);
        return ResponseEntity.ok(mapToResponse(updatedState));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteState(@PathVariable Long id) {
        stateService.delete(id);
        return ResponseEntity.noContent().build();
    }

    private StateResponse mapToResponse(State state) {
        StateResponse response = new StateResponse();
        response.setId(state.getId());
        response.setName(state.getName());

        if (state.getCountry() != null) {
            response.setCountryId(state.getCountry().getId());
            response.setCountryName(state.getCountry().getName());
        }

        return response;
    }
}