package com.ascent.solidus.web.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.hibernate5.jakarta.Hibernate5JakartaModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // Register Hibernate5Module to handle lazy loading
        Hibernate5JakartaModule hibernate5Module = new Hibernate5JakartaModule();
        
        // Don't force lazy loading - this prevents the LazyInitializationException
        hibernate5Module.configure(Hibernate5JakartaModule.Feature.FORCE_LAZY_LOADING, false);
        
        // Serialize proxied objects as their actual type
        hibernate5Module.configure(Hibernate5JakartaModule.Feature.REPLACE_PERSISTENT_COLLECTIONS, true);
        
        mapper.registerModule(hibernate5Module);
        
        return mapper;
    }
}