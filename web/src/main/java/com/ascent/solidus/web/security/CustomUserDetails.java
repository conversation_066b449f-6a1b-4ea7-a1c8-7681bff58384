package com.ascent.solidus.web.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

public class CustomUserDetails extends User {
    private Long id;
    private String organizationId;

    public void setId(Long id) {
        this.id = id;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    private String factoryId;

    public CustomUserDetails(String username, String password,
                            Collection<? extends GrantedAuthority> authorities,
                            Long id, String organizationId, String factoryId) {
        super(username, password, authorities);
        this.id = id;
        this.organizationId = organizationId;
        this.factoryId = factoryId;
    }

    public Long getId() {
        return id;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public String getFactoryId() {
        return factoryId;
    }
}