package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.services.tenant.TenantFeatureRoleManager;
import com.ascent.solidus.services.tenant.FeatureConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST Controller for managing tenant-feature-role configurations
 */
@RestController
@RequestMapping("/api/tenant-management")
@CrossOrigin(origins = "*")
public class TenantManagementController {
    
    @Autowired
    private TenantFeatureRoleManager tenantFeatureRoleManager;
    
    @Autowired
    private FeatureConfigurationService featureConfigurationService;
    
    /**
     * Get all features accessible to a user
     */
    @GetMapping("/user/{userId}/features")
    public ResponseEntity<List<String>> getUserAccessibleFeatures(@PathVariable Long userId) {
        try {
            List<String> features = tenantFeatureRoleManager.getUserAccessibleFeatures(userId);
            return ResponseEntity.ok(features);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get user's permissions for a specific feature
     */
    @GetMapping("/user/{userId}/features/{featureId}/permissions")
    public ResponseEntity<List<PermissionAction>> getUserFeaturePermissions(
            @PathVariable Long userId, 
            @PathVariable String featureId) {
        try {
            List<PermissionAction> permissions = tenantFeatureRoleManager.getUserFeaturePermissions(userId, featureId);
            return ResponseEntity.ok(permissions);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get all modules within a feature that a user can access
     */
    @GetMapping("/user/{userId}/features/{featureId}/modules")
    public ResponseEntity<List<String>> getUserAccessibleModules(
            @PathVariable Long userId, 
            @PathVariable String featureId) {
        try {
            List<String> modules = tenantFeatureRoleManager.getUserAccessibleModules(userId, featureId);
            return ResponseEntity.ok(modules);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Check if user can perform a specific action on a feature
     */
    @GetMapping("/user/{userId}/features/{featureId}/can-perform/{action}")
    public ResponseEntity<Boolean> canUserPerformAction(
            @PathVariable Long userId, 
            @PathVariable String featureId,
            @PathVariable String action) {
        try {
            PermissionAction permissionAction = PermissionAction.valueOf(action.toUpperCase());
            boolean canPerform = tenantFeatureRoleManager.canUserPerformAction(userId, featureId, permissionAction);
            return ResponseEntity.ok(canPerform);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Check if user can access a specific module within a feature
     */
    @GetMapping("/user/{userId}/features/{featureId}/modules/{moduleName}/access")
    public ResponseEntity<Boolean> canUserAccessModule(
            @PathVariable Long userId, 
            @PathVariable String featureId,
            @PathVariable String moduleName) {
        try {
            boolean canAccess = tenantFeatureRoleManager.canUserAccessModule(userId, featureId, moduleName);
            return ResponseEntity.ok(canAccess);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get tenant-specific module configuration for a feature
     */
    @GetMapping("/tenant/{tenantId}/features/{featureId}/config")
    public ResponseEntity<Map<String, Object>> getTenantModuleConfig(
            @PathVariable Long tenantId, 
            @PathVariable String featureId) {
        try {
            Map<String, Object> config = tenantFeatureRoleManager.getTenantModuleConfig(tenantId, featureId);
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get filtered module configuration based on user permissions and tenant settings
     */
    @GetMapping("/user/{userId}/tenant/{tenantId}/features/{featureId}/config")
    public ResponseEntity<Map<String, Object>> getFilteredModuleConfig(
            @PathVariable Long userId,
            @PathVariable Long tenantId, 
            @PathVariable String featureId) {
        try {
            Map<String, Object> config = tenantFeatureRoleManager.getFilteredModuleConfig(userId, tenantId, featureId);
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get custom fields for a module specific to a tenant
     */
    @GetMapping("/tenant/{tenantId}/features/{featureId}/modules/{moduleName}/custom-fields")
    public ResponseEntity<List<Map<String, Object>>> getTenantModuleCustomFields(
            @PathVariable Long tenantId,
            @PathVariable String featureId,
            @PathVariable String moduleName) {
        try {
            List<Map<String, Object>> customFields = tenantFeatureRoleManager
                    .getTenantModuleCustomFields(tenantId, featureId, moduleName);
            return ResponseEntity.ok(customFields);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get summary of tenant's feature access
     */
    @GetMapping("/tenant/{tenantId}/summary")
    public ResponseEntity<TenantFeatureRoleManager.TenantFeatureSummary> getTenantFeatureSummary(
            @PathVariable Long tenantId) {
        try {
            TenantFeatureRoleManager.TenantFeatureSummary summary = 
                    tenantFeatureRoleManager.getTenantFeatureSummary(tenantId);
            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Validate if a tenant-feature-role combination is valid
     */
    @GetMapping("/validate/tenant/{tenantId}/feature/{featureId}/role/{roleName}")
    public ResponseEntity<TenantFeatureRoleManager.ValidationResult> validateTenantFeatureRoleAccess(
            @PathVariable Long tenantId,
            @PathVariable String featureId,
            @PathVariable String roleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            TenantFeatureRoleManager.ValidationResult result = 
                    tenantFeatureRoleManager.validateTenantFeatureRoleAccess(tenantId, featureId, role);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Reload all configurations from JSON files
     */
    @PostMapping("/reload-configurations")
    public ResponseEntity<String> reloadConfigurations() {
        try {
            featureConfigurationService.initializeConfigurations();
            return ResponseEntity.ok("Configurations reloaded successfully");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error reloading configurations: " + e.getMessage());
        }
    }
}
