package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.tenant.Organization;
import com.ascent.solidus.core.security.UserPrincipal;
import com.ascent.solidus.services.tenant.OrganizationService;
//import com.ascent.solidus.web.security.CurrentUser;
import com.ascent.solidus.web.security.CustomUserDetails;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/organizations")
public class OrganizationController {

    @Autowired
    private OrganizationService organizationService;

    @PostMapping
    public ResponseEntity<Organization> create(@RequestBody Organization organization) {
        Long tenantId = getCurrentUserTenantId();
        Organization saved = organizationService.createOrganization(organization, tenantId);
        return new ResponseEntity<>(saved, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Organization> update(@PathVariable Long id, @RequestBody Organization organization) {
        Organization updated = organizationService.updateOrganization(id, organization);
        return new ResponseEntity<>(updated, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        organizationService.deactivateOrganization(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<Organization>> getAll() {
        // Get tenant ID from authenticated user
        Long tenantId = getCurrentUserTenantId();
        List<Organization> organizations = organizationService.findByTenant(tenantId);
        return new ResponseEntity<>(organizations, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Organization> getOne(@PathVariable Long id) {
        Organization organization = organizationService.findById(id);
        return new ResponseEntity<>(organization, HttpStatus.OK);
    }

    /**
     * Helper method to get the current user's tenant ID
     */
    private Long getCurrentUserTenantId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            return userPrincipal.getTenantId();
        }
        // Default tenant ID if not found in authentication
        throw new RuntimeException("No authenticated user found or principal is not UserPrincipal");
    }
}
