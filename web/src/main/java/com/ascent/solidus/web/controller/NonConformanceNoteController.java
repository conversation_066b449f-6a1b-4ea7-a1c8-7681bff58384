package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.NonConformanceNote;
import com.ascent.solidus.services.NonConformanceNoteService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/nonconformancenote")
public class NonConformanceNoteController {
  private final NonConformanceNoteService service;

  public NonConformanceNoteController(NonConformanceNoteService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public NonConformanceNote getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public NonConformanceNote create(@RequestBody NonConformanceNote entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public NonConformanceNote update(@PathVariable("id") String id,
      @RequestBody NonConformanceNote entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }
}
