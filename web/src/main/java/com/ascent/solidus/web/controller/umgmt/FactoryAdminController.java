package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.services.umgmt.FactoryAdminService;
import com.ascent.solidus.web.dto.AppUserResponse;
import com.ascent.solidus.web.security.CurrentUser;
import com.ascent.solidus.web.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/factoryadmins")
public class FactoryAdminController {

    @Autowired
    FactoryAdminService factoryAdminService;

    @PostMapping
    public ResponseEntity<AppUserResponse> create(@RequestBody AppUser appUser) {
        AppUser saved = factoryAdminService.save(appUser);
        return new ResponseEntity<>(mapToResponse(saved), HttpStatus.CREATED);
    }

    @PutMapping
    public ResponseEntity<AppUserResponse> update(@RequestBody AppUser appUser) {
        AppUser updated = factoryAdminService.update(appUser);
        return new ResponseEntity<>(mapToResponse(updated), HttpStatus.OK);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        // Implement delete functionality if needed
        return ResponseEntity.ok().build();
    }
    
    @GetMapping
    public ResponseEntity<ListResponse> getAll(
            HttpServletRequest request,
            @CurrentUser CustomUserDetails currentUser) {
        
        SearchCriteria searchCriteria = extractSearchCriteria(request);
        AppUser loggedInUser = new AppUser();
        loggedInUser.setId(currentUser.getId());
        
        ListResponse result = factoryAdminService.findAll(searchCriteria, loggedInUser);
        result.setDraw(searchCriteria.getDraw());
        
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<AppUserResponse> getOne(@PathVariable Long id) {
        AppUser appUser = factoryAdminService.getOne(id);
        return new ResponseEntity<>(mapToResponse(appUser), HttpStatus.OK);
    }
    
    @PostMapping("/resendcredentials")
    public ResponseEntity<AppUserResponse> resendMail(@RequestParam Long appUserId) {
        AppUser appUser = factoryAdminService.resendMail(appUserId);
        return new ResponseEntity<>(mapToResponse(appUser), HttpStatus.OK);
    }
    
    private AppUserResponse mapToResponse(AppUser appUser) {
        AppUserResponse response = new AppUserResponse();
        response.setId(appUser.getId());
        response.setFirstName(appUser.getFirstName());
        response.setLastName(appUser.getLastName());
        response.setEmail(appUser.getEmail());
        response.setMobile(appUser.getPhone());
        response.setUsername(appUser.getUsername());
        
        if (appUser.getFactory() != null) {
            response.setFactoryId(appUser.getFactory().getId());
        }
        
        return response;
    }
    
    private SearchCriteria extractSearchCriteria(HttpServletRequest request) {
        SearchCriteria searchCriteria = new SearchCriteria();
        
        // Extract parameters from request
        String draw = request.getParameter("draw");
        String start = request.getParameter("start");
        String length = request.getParameter("length");
        
        if (draw != null) {
            searchCriteria.setDraw(Integer.parseInt(draw));
        }
        
        if (start != null) {
            searchCriteria.setStart(Integer.parseInt(start));
        }
        
        if (length != null) {
            searchCriteria.setLength(Integer.parseInt(length));
        }
        
        // Add more parameters as needed
        
        return searchCriteria;
    }
}