package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.model.dto.LoginRequest;
import com.ascent.solidus.core.model.dto.LoginResponse;
import com.ascent.solidus.core.security.UserPrincipal;
import com.ascent.solidus.services.auth.UserService;
import com.ascent.solidus.web.security.CustomUserDetails;
import com.ascent.solidus.web.security.JwtTokenProvider;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.security.SecureRandom;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/auth")
public class LoginController {

    private final AuthenticationManager authenticationManager;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final JwtTokenProvider jwtTokenProvider;
//    private final EmailService emailService;

    public LoginController(
            AuthenticationManager authenticationManager,
            PasswordEncoder passwordEncoder,
            UserService userService,
            JwtTokenProvider jwtTokenProvider) {
        this.authenticationManager = authenticationManager;
        this.passwordEncoder = passwordEncoder;
        this.userService = userService;
        this.jwtTokenProvider = jwtTokenProvider;
//        this.emailService = emailService;
    }

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsername(),
                loginRequest.getPassword()
            )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // Get user details
        Object principal = authentication.getPrincipal();

        // Check if principal is UserPrincipal
        if (!(principal instanceof UserPrincipal)) {
            throw new IllegalStateException("Authentication principal is not of type UserPrincipal");
        }

        UserPrincipal userPrincipal = (UserPrincipal) principal;
        String token = jwtTokenProvider.generateToken(authentication);
        
        LoginResponse response = new LoginResponse(
            token,
            userPrincipal.getUsername(),
            userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toSet()),
            userPrincipal.getOrganizationId() != null ? userPrincipal.getOrganizationId().toString() : null,
            userPrincipal.getFactoryId()
        );

        return ResponseEntity.ok(response);
    }

    @PostMapping("/reset-password")
    public ResponseEntity<String> resetPassword(@RequestParam String username) {
        // Generate new password
        String newPassword = generateRandomPassword(12);
        String encodedPassword = passwordEncoder.encode(newPassword);
        
        // Update user's password in database
        userService.updatePassword(username, encodedPassword);
        
        // Send new password to user's email
//        emailService.sendPasswordResetEmail(username, newPassword);
        
        return ResponseEntity.ok("Password reset successful. New password has been sent to your email.");
    }

    private String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            password.append(chars.charAt(index));
        }
        
        return password.toString();
    }
} 