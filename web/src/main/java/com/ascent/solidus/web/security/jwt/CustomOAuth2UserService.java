package com.ascent.solidus.web.security.jwt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;

@Service
public class CustomOAuth2UserService extends DefaultOAuth2UserService {

    @Autowired
    private AppUserRepository appUserRepository;

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        OidcUser oidcUser = (OidcUser) super.loadUser(userRequest);

        // Extract tenant ID from ID token
        String tenantId = oidcUser.getClaimAsString("tid");
        String objectId = oidcUser.getClaimAsString("oid");
        String email = oidcUser.getEmail();
        String firstName = oidcUser.getGivenName();
        String lastName = oidcUser.getFamilyName();

        // Save or update AppUser
        AppUser user = appUserRepository.findByEmailAndActive(email,true)
                .orElseGet(() -> new AppUser());
        user.setEmail(email);
        user.setUsername(email); // or use a preferred username claim
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setAzureTenantId(tenantId);
        user.setAzureObjectId(objectId);

        // Optional: associate tenant and roles here
        user.setEnabled(true);
        appUserRepository.save(user);

        // Wrap as Spring Security user
        return oidcUser;
    }
}

