package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.Product;
import com.ascent.solidus.core.domain.module.ProductFamily;
import com.ascent.solidus.services.ProductService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/product")
public class ProductController {
  private final ProductService service;

  public ProductController(ProductService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public Product getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public Product create(@RequestBody Product entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public Product update(@PathVariable("id") String id, @RequestBody Product entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }

  @GetMapping("/{id}/productfamily")
  public ResponseEntity<ProductFamily> getProductFamilyForProduct(@PathVariable("id") String id) {
    ProductFamily entity = service.getProductFamilyByProductId(id);
    return entity != null ? ResponseEntity.ok(entity) : ResponseEntity.notFound().build();
  }

  @GetMapping("/{productId}/referencing-productfamilys")
  public ResponseEntity<List<Product>> getProductFamilysReferencingProduct(
      @PathVariable("productId") String productId) {
    return ResponseEntity.ok(service.findProductFamilyByProductId(productId));
  }
}
