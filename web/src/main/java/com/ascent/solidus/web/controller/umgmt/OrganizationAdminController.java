package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.services.umgmt.OrganizationAdminService;
import com.ascent.solidus.web.dto.AppUserResponse;
import com.ascent.solidus.web.security.CurrentUser;
import com.ascent.solidus.web.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/organizationadmins")
public class OrganizationAdminController {

    @Autowired
    OrganizationAdminService organizationAdminService;

    @PostMapping
    public ResponseEntity<AppUserResponse> create(@RequestBody AppUser appUser) {
        AppUser saved = organizationAdminService.save(appUser);
        return new ResponseEntity<>(mapToResponse(saved), HttpStatus.CREATED);
    }

    @PutMapping
    public ResponseEntity<AppUserResponse> update(@RequestBody AppUser appUser) {
        AppUser updated = organizationAdminService.update(appUser);
        return new ResponseEntity<>(mapToResponse(updated), HttpStatus.OK);
    }
    
    @GetMapping
    public ResponseEntity<ListResponse> getAll(
            HttpServletRequest request,
            @CurrentUser CustomUserDetails currentUser) {
        
        SearchCriteria searchCriteria = extractSearchCriteria(request);
        AppUser loggedInUser = new AppUser();
        loggedInUser.setId(currentUser.getId());
        
        ListResponse result = organizationAdminService.findAll(searchCriteria, loggedInUser);
        result.setDraw(searchCriteria.getDraw());
        
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<AppUserResponse> getOne(@PathVariable Long id) {
        AppUser appUser = organizationAdminService.getOne(id);
        return new ResponseEntity<>(mapToResponse(appUser), HttpStatus.OK);
    }
    
    private AppUserResponse mapToResponse(AppUser appUser) {
        AppUserResponse response = new AppUserResponse();
        response.setId(appUser.getId());
        response.setFirstName(appUser.getFirstName());
//        response.setMiddleName(appUser.getMiddleName());
        response.setLastName(appUser.getLastName());
        response.setEmail(appUser.getEmail());
//        response.setGender(appUser.getGender());
        response.setMobile(appUser.getPhone());
        
        if (appUser.getOrganization() != null) {
            response.setOrganizationId(appUser.getOrganization().getId());
            response.setOrganizationName(appUser.getOrganization().getName());
        }
        
        return response;
    }
    
    private SearchCriteria extractSearchCriteria(HttpServletRequest request) {
        SearchCriteria criteria = new SearchCriteria();
        
        // Extract pagination parameters
        String start = request.getParameter("start");
        String length = request.getParameter("length");
        String draw = request.getParameter("draw");
        
        if (start != null) {
            criteria.setStart(Integer.parseInt(start));
        }
        
        if (length != null) {
            criteria.setLength(Integer.parseInt(length));
        }
        
        if (draw != null) {
            criteria.setDraw(Integer.parseInt(draw));
        }
        
        // Extract search parameters
        String searchValue = request.getParameter("search[value]");
        if (searchValue != null && !searchValue.isEmpty()) {
            criteria.setSearchValue(searchValue);
        }
        
        // Extract sorting parameters
        String sortColumn = request.getParameter("order[0][column]");
        String sortDir = request.getParameter("order[0][dir]");
        
        if (sortColumn != null && sortDir != null) {
            String columnName = request.getParameter("columns[" + sortColumn + "][data]");
            criteria.setSortColumn(columnName);
            criteria.setSortDirection(sortDir);
        }
        
        return criteria;
    }
}