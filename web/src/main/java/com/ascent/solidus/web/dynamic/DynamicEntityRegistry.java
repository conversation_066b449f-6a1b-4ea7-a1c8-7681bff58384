//package com.ascent.solidus.web.dynamic;
//
//
//import com.ascent.solidus.core.domain.module.ModuleConfig;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//
//public interface DynamicEntityRegistry {
//    void registerEntity(ModuleConfig moduleConfig) throws IOException;
//    Class<?> getEntityClass(String entityName);
//    boolean isRegistered(String entityName);
//    Set<String> getRegisteredEntities();
//    ModuleConfig getModuleConfig(String entityName);
//}
//
