package com.ascent.solidus.web.controller.permissions;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.permissions.RoleModulePermission;
import com.ascent.solidus.services.permissions.RoleModulePermissionService;
import com.ascent.solidus.services.permissions.RoleModulePermissionService.ModulePermissionRequest;
import com.ascent.solidus.services.permissions.ModuleStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for managing role-module permissions
 * This supports the UI shown in the image for role-based module assignment
 */
@RestController
@RequestMapping("/api/role-module-permissions")
@CrossOrigin(origins = "*")
public class RoleModulePermissionController {
    
    @Autowired
    private RoleModulePermissionService roleModulePermissionService;

    @Autowired
    private ModuleStructureService moduleStructureService;
    
    /**
     * Get all available roles for the dropdown
     */
    @GetMapping("/roles")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<RoleName[]> getAllRoles() {
        return ResponseEntity.ok(RoleName.values());
    }
    
    /**
     * Get permissions for a specific role (for loading the checkboxes)
     */
    @GetMapping("/roles/{roleName}/permissions")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<List<RoleModulePermission>> getRolePermissions(@PathVariable String roleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            List<RoleModulePermission> permissions = roleModulePermissionService.getRolePermissions(role);
            return ResponseEntity.ok(permissions);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get permissions grouped by parent modules (for tree structure in UI)
     */
    @GetMapping("/roles/{roleName}/permissions/grouped")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<Map<String, List<RoleModulePermission>>> getRolePermissionsGrouped(@PathVariable String roleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            Map<String, List<RoleModulePermission>> groupedPermissions = 
                    roleModulePermissionService.getRolePermissionsGrouped(role);
            return ResponseEntity.ok(groupedPermissions);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Assign modules to a role (Save button functionality)
     */
    @PostMapping("/roles/{roleName}/assign")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<String> assignModulesToRole(
            @PathVariable String roleName,
            @RequestBody List<ModulePermissionRequest> moduleRequests) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            roleModulePermissionService.assignModulesToRole(role, moduleRequests);
            return ResponseEntity.ok("Modules assigned successfully to role: " + roleName);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Invalid role name: " + roleName);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error assigning modules: " + e.getMessage());
        }
    }
    
    /**
     * Check if a role has access to a specific module
     */
    @GetMapping("/roles/{roleName}/modules/{moduleName}/access")
    public ResponseEntity<Boolean> checkRoleModuleAccess(
            @PathVariable String roleName,
            @PathVariable String moduleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            boolean hasAccess = roleModulePermissionService.hasRoleAccessToModule(role, moduleName);
            return ResponseEntity.ok(hasAccess);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Check if a role has specific permission for a module
     */
    @GetMapping("/roles/{roleName}/modules/{moduleName}/permissions/{permissionType}")
    public ResponseEntity<Boolean> checkRoleModulePermission(
            @PathVariable String roleName,
            @PathVariable String moduleName,
            @PathVariable String permissionType) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            boolean hasPermission = roleModulePermissionService.hasRolePermission(role, moduleName, permissionType);
            return ResponseEntity.ok(hasPermission);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get all accessible modules for a role
     */
    @GetMapping("/roles/{roleName}/accessible-modules")
    public ResponseEntity<List<String>> getAccessibleModules(@PathVariable String roleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            List<String> modules = roleModulePermissionService.getAccessibleModules(role);
            return ResponseEntity.ok(modules);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get accessible parent modules for a role
     */
    @GetMapping("/roles/{roleName}/accessible-parent-modules")
    public ResponseEntity<List<String>> getAccessibleParentModules(@PathVariable String roleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            List<String> modules = roleModulePermissionService.getAccessibleParentModules(role);
            return ResponseEntity.ok(modules);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get accessible sub-modules for a role and parent module
     */
    @GetMapping("/roles/{roleName}/parent-modules/{parentModuleName}/accessible-sub-modules")
    public ResponseEntity<List<String>> getAccessibleSubModules(
            @PathVariable String roleName,
            @PathVariable String parentModuleName) {
        try {
            RoleName role = RoleName.valueOf(roleName);
            List<String> modules = roleModulePermissionService.getAccessibleSubModules(role, parentModuleName);
            return ResponseEntity.ok(modules);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get all available modules in hierarchical structure (for UI tree)
     */
    @GetMapping("/modules/structure")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<List<ModuleStructureService.ModuleStructure>> getModuleStructures() {
        List<ModuleStructureService.ModuleStructure> structures = moduleStructureService.getAllModuleStructures();
        return ResponseEntity.ok(structures);
    }

    /**
     * Get all available modules flattened (for permission assignment)
     */
    @GetMapping("/modules/flattened")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<List<ModuleStructureService.ModuleInfo>> getModulesFlattened() {
        List<ModuleStructureService.ModuleInfo> modules = moduleStructureService.getAllModulesFlattened();
        return ResponseEntity.ok(modules);
    }
}
