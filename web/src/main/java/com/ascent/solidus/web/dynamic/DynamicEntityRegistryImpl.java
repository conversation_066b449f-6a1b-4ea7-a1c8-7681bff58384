//package com.ascent.solidus.web.dynamic;
//
//
//import com.ascent.solidus.core.dao.DynamicEntityGenerator;
////import com.ascent.solidus.core.dao.DynamicEntityRegistry;
//import com.ascent.solidus.core.dao.DynamicRepositoryGenerator;
//import com.ascent.solidus.core.dao.DynamicServiceGenerator;
//import com.ascent.solidus.core.domain.module.ModuleConfig;
//import com.ascent.solidus.web.controller.DynamicControllerGenerator;
//import com.squareup.javapoet.JavaFile;
//import jakarta.persistence.EntityManagerFactory;
//import jakarta.persistence.metamodel.Metamodel;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//
//import java.io.IOException;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//
//@Component
//public class DynamicEntityRegistryImpl implements DynamicEntityRegistry {
//    private final Map<String, Class<?>> entityClasses = new ConcurrentHashMap<>();
//    private final Map<String, ModuleConfig> moduleConfigs = new ConcurrentHashMap<>();
//    private final DynamicEntityGenerator entityGenerator;
//    private final DynamicRepositoryGenerator repositoryGenerator;
//    private final DynamicServiceGenerator serviceGenerator;
//    private final DynamicControllerGenerator controllerGenerator;
//
//
//    private final EntityManagerFactory emf;
//
//    @Autowired
//    public DynamicEntityRegistryImpl(DynamicEntityGenerator entityGenerator, DynamicRepositoryGenerator repositoryGenerator, DynamicServiceGenerator serviceGenerator,DynamicControllerGenerator controllerGenerator,
//                                     EntityManagerFactory emf) {
//        this.entityGenerator = entityGenerator;
//        this.repositoryGenerator = repositoryGenerator;
//        this.serviceGenerator = serviceGenerator;
//        this.controllerGenerator = controllerGenerator;
//        this.emf = emf;
//    }
//
//    @Override
//    public void registerEntity(ModuleConfig moduleConfig) throws IOException {
//        JavaFile javaFile = entityGenerator.generateEntityClass(moduleConfig);
////        entityClasses.put(moduleConfig.getModuleName(), entityClass);
////        moduleConfigs.put(moduleConfig.getModuleName(), moduleConfig);
//
//        Path outputDir = Paths.get("/Users/<USER>/Downloads/solidus-platform/web/src/main/java"); // Or configurable directory
//        javaFile.writeTo(outputDir);
//
//        // Generate repository interface
//        try {
//            Class<?> entityClass = Class.forName("com.ascent.solidus.core.domain.module." + moduleConfig.getModuleName());
//            repositoryGenerator.generateRepositoryInterface(entityClass);
//            serviceGenerator.generateServiceClass(entityClass);
//        } catch (ClassNotFoundException e) {
//            throw new RuntimeException("Failed to load generated entity class", e);
//        }
//
//        // 3. Optionally store config for future use (but NOT the class)
//        moduleConfigs.put(moduleConfig.getModuleName(), moduleConfig);
//
//        // Register with Hibernate
////        Metamodel metamodel = emf.getMetamodel();
////        if (metamodel instanceof HibernateMetamodel) {
////            ((HibernateMetamodel) metamodel).getTypeConfiguration()
////                    .getScope()
////                    .addEntityBinding(entityClass);
////        }
//    }
//
//    @Override
//    public Class<?> getEntityClass(String entityName) {
//        Class<?> clazz = entityClasses.get(entityName);
//        if (clazz == null) {
//            throw new IllegalArgumentException("Unknown entity type: " + entityName);
//        }
//        return clazz;
//    }
//
//    @Override
//    public boolean isRegistered(String entityName) {
//        return entityClasses.containsKey(entityName);
//    }
//
//    @Override
//    public Set<String> getRegisteredEntities() {
//        return entityClasses.keySet();
//    }
//
//    @Override
//    public ModuleConfig getModuleConfig(String entityName) {
//        return moduleConfigs.get(entityName);
//    }
//}