//package com.ascent.solidus.web.controller.module;
//
//import com.ascent.solidus.core.constants.RoleName;
//import com.ascent.solidus.core.domain.module.ModulePermission;
//import com.ascent.solidus.services.module.ModulePermissionService;
//import com.ascent.solidus.web.dto.ModuleAssignmentRequest;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/module-permissions")
//@RequiredArgsConstructor
//public class ModulePermissionController {
//
//    private final ModulePermissionService modulePermissionService;
//
//    @GetMapping("/{roleName}")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<ModulePermission>> getModulePermissionsByRole(
//            @PathVariable("roleName") String roleNameStr) {
//        RoleName roleName = RoleName.valueOf(roleNameStr);
//        return ResponseEntity.ok(modulePermissionService.findByRole(roleName));
//    }
//
//    @GetMapping("/{roleName}/module-ids")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<Long>> getModuleIdsByRole(
//            @PathVariable("roleName") String roleNameStr) {
//        RoleName roleName = RoleName.valueOf(roleNameStr);
//        return ResponseEntity.ok(modulePermissionService.getModuleIdsByRole(roleName));
//    }
//
//    @PostMapping("/{roleName}/assign")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<Void> assignModulesToRole(
//            @PathVariable("roleName") String roleNameStr,
//            @RequestBody List<Long> moduleIds) {
//        RoleName roleName = RoleName.valueOf(roleNameStr);
//        modulePermissionService.assignModulesToRole(roleName, moduleIds);
//        return ResponseEntity.ok().build();
//    }
//
//    @PostMapping("/{roleName}/assign-with-submodules")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<Void> assignModuleWithSubmodulesToRole(
//            @PathVariable("roleName") String roleNameStr,
//            @RequestBody ModuleAssignmentRequest request) {
//        RoleName roleName = RoleName.valueOf(roleNameStr);
//        modulePermissionService.assignModuleWithSubmodulesToRole(
//                roleName, request.getParentModuleId(), request.getSubModuleIds());
//        return ResponseEntity.ok().build();
//    }
//}