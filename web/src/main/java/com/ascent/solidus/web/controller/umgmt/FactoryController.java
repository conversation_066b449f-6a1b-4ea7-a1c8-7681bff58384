package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.module.Factory;
import com.ascent.solidus.core.vo.ListResponse;
import com.ascent.solidus.core.vo.SearchCriteria;
import com.ascent.solidus.services.umgmt.FactoryService;
import com.ascent.solidus.web.security.CurrentUser;
import com.ascent.solidus.web.security.CustomUserDetails;
import com.ascent.solidus.web.dto.FactoryResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * REST controller for managing factories
 */
@RestController
@RequestMapping("/factories")
public class FactoryController {

    @Autowired
    FactoryService factoryService;

    /**
     * Create a new factory
     *
     * @param factory The factory to create
     * @return The created factory
     */
    @PostMapping
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN')")
    public ResponseEntity<FactoryResponse> create(@RequestBody Factory factory) {
        Factory saved = factoryService.save(factory);
        return new ResponseEntity<>(mapToResponse(saved), HttpStatus.CREATED);
    }

    /**
     * Update an existing factory
     *
     * @param factory The factory to update
     * @return The updated factory
     */
    @PutMapping
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<FactoryResponse> update(@RequestBody Factory factory) {
        Factory updated = factoryService.update(factory);
        return new ResponseEntity<>(mapToResponse(updated), HttpStatus.OK);
    }

    /**
     * Delete a factory
     *
     * @param id The ID of the factory to delete
     * @return No content
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        factoryService.delete(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get all factories with pagination and filtering
     *
     * @param request The HTTP request
     * @return A list of factories
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<ListResponse> getAll(HttpServletRequest request) {
        SearchCriteria searchCriteria = extractSearchCriteria(request);
        ListResponse result = factoryService.findAll(searchCriteria);
        result.setDraw(searchCriteria.getDraw());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * Get a factory by ID
     *
     * @param id The ID of the factory
     * @return The factory
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_ORGANIZATION_ADMIN', 'ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<FactoryResponse> getOne(@PathVariable Long id) {
        Factory factory = factoryService.getOne(id);
        return new ResponseEntity<>(mapToResponse(factory), HttpStatus.OK);
    }

    /**
     * Get factory by current user
     *
     * @param currentUser The current user
     * @return The factory associated with the user
     */
    @GetMapping("/current")
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUPERVISOR', 'ROLE_INSPECTOR')")
    public ResponseEntity<FactoryResponse> getFactoryByUser(@CurrentUser CustomUserDetails currentUser) {
        AppUser loggedInUser = new AppUser();
        loggedInUser.setId(currentUser.getId());

        Factory factory = factoryService.getFactoryByUser(loggedInUser);
        return new ResponseEntity<>(mapToResponse(factory), HttpStatus.OK);
    }

    /**
     * Extract search criteria from request
     *
     * @param request The HTTP request
     * @return The search criteria
     */
    private SearchCriteria extractSearchCriteria(HttpServletRequest request) {
        SearchCriteria searchCriteria = new SearchCriteria();

        // Extract parameters from request
        String draw = request.getParameter("draw");
        String start = request.getParameter("start");
        String length = request.getParameter("length");
        String search = request.getParameter("search[value]");

        if (draw != null) {
            searchCriteria.setDraw(Integer.parseInt(draw));
        }

        if (start != null) {
            searchCriteria.setStart(Integer.parseInt(start));
        }

        if (length != null) {
            searchCriteria.setLength(Integer.parseInt(length));
        }

        if (search != null) {
            // Use the correct method based on your SearchCriteria class
            // Option 1: If your SearchCriteria has a searchValue field
            searchCriteria.setSearchValue(search);

            // Option 2: If your SearchCriteria uses a different field name
            // searchCriteria.setQuery(search);

            // Option 3: If your SearchCriteria uses a map for parameters
            // searchCriteria.getParameters().put("search", search);
        }

        return searchCriteria;
    }

    /**
     * Map Factory to FactoryResponse
     *
     * @param factory The factory to map
     * @return The factory response
     */
    private FactoryResponse mapToResponse(Factory factory) {
        FactoryResponse response = new FactoryResponse();
        response.setId(factory.getId());
        response.setName(factory.getName());
        response.setContactPersonName(factory.getContactPersonName());
        response.setEmail(factory.getEmail());
        response.setMobile(factory.getMobile());
        response.setAddressLine1(factory.getAddressLine1());
        response.setAddressLine2(factory.getAddressLine2());
        response.setArea(factory.getArea());
        response.setZipCode(factory.getZipCode());
        response.setOtherContactNumber(factory.getOtherContactNumber());

        if (factory.getCity() != null) {
            response.setCityId(factory.getCity().getId());
            response.setCityName(factory.getCity().getName());
        }

        if (factory.getState() != null) {
            response.setStateId(factory.getState().getId());
            response.setStateName(factory.getState().getName());
        }

        if (factory.getCountry() != null) {
            response.setCountryId(factory.getCountry().getId());
            response.setCountryName(factory.getCountry().getName());
        }

        return response;
    }
}