package com.ascent.solidus.web.controller.umgmt;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import com.ascent.solidus.core.security.UserPrincipal;
import com.ascent.solidus.web.security.JwtTokenProvider;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;

@RestController
@RequestMapping("/auth")
public class OAuth2LoginController {

    @Autowired
    AppUserRepository appUserRepository;

    @Autowired
    JwtTokenProvider jwtTokenProvider;

    @GetMapping("/oauth2-login-success")
    public ResponseEntity<?> handleOAuth2Success(Authentication authentication, HttpServletResponse response) throws IOException {
        if (!(authentication.getPrincipal() instanceof OidcUser oidcUser)) {
            return ResponseEntity.badRequest().body("Invalid user principal");
        }

        // Extract values from Azure ID token
        String email = oidcUser.getEmail();
        String firstName = oidcUser.getGivenName();
        String lastName = oidcUser.getFamilyName();
        String tenantId = oidcUser.getClaimAsString("tid");  // Azure AD Tenant ID
        String objectId = oidcUser.getClaimAsString("oid");  // Azure AD Object ID

        // Check if user exists
        AppUser user = appUserRepository.findByEmailAndActive(email,true).orElse(null);
        if (user == null) {
            user = new AppUser();
            user.setEmail(email);
            user.setUsername(email);
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEnabled(true);
            user.setActive(true);
            user.setAccountNonLocked(true);
        }

        // Store Azure AD tenant ID
        user.setAzureTenantId(tenantId);

        appUserRepository.save(user);

        // Build UserPrincipal
        UserPrincipal principal = UserPrincipal.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .azureTenantId(user.getAzureTenantId())
                .organizationId(user.getOrganization() != null ? user.getOrganization().getId() : null)
                .factoryId(user.getFactory() != null ? user.getFactory().getId().toString() : null)
                .enabled(user.isEnabled())
                .accountNonLocked(user.isAccountNonLocked())
                .authorities(Collections.emptyList()) // Add roles if needed
                .build();



        // Generate JWT token
        String jwt = jwtTokenProvider.generateToken(
                new org.springframework.security.authentication.UsernamePasswordAuthenticationToken(
                        principal, null, principal.getAuthorities()
                )
        );

        // Return JWT
        return ResponseEntity.ok().body(Collections.singletonMap("token", jwt));
    }
}
