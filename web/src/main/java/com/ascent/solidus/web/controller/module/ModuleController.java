//package com.ascent.solidus.web.controller.module;
//
//import com.ascent.solidus.core.domain.module.Module;
//import com.ascent.solidus.services.module.ModuleService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/modules")
//@RequiredArgsConstructor
//public class ModuleController {
//
//    private final ModuleService moduleService;
//
//    @GetMapping
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<Module>> getAllModules() {
//        return ResponseEntity.ok(moduleService.findAllModules());
//    }
//
//    @GetMapping("/standalone")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<Module>> getAllStandaloneModules() {
//        return ResponseEntity.ok(moduleService.findAllStandaloneModules());
//    }
//
//    @GetMapping("/component")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<Module>> getAllComponentModules() {
//        return ResponseEntity.ok(moduleService.findAllComponentModules());
//    }
//
//    @GetMapping("/{id}")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<Module> getModuleById(@PathVariable Long id) {
//        return ResponseEntity.ok(moduleService.findById(id));
//    }
//
//    @GetMapping("/name/{name}")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<Module> getModuleByName(@PathVariable String name) {
//        return ResponseEntity.ok(moduleService.findByName(name));
//    }
//
//    @GetMapping("/{id}/submodules")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ORGANIZATION_ADMIN', 'ROLE_FACTORY_ADMIN')")
//    public ResponseEntity<List<Module>> getSubModules(@PathVariable Long id) {
//        return ResponseEntity.ok(moduleService.findSubModules(id));
//    }
//
//    @PostMapping("/standalone")
//    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
//    public ResponseEntity<Module> createStandaloneModule(@RequestBody Module module) {
//        return new ResponseEntity<>(moduleService.createStandaloneModule(module), HttpStatus.CREATED);
//    }
//
//    @PostMapping("/component")
//    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
//    public ResponseEntity<Module> createComponentModule(@RequestBody Module module) {
//        return new ResponseEntity<>(moduleService.createComponentModule(module), HttpStatus.CREATED);
//    }
//
//    @PutMapping("/{id}")
//    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
//    public ResponseEntity<Module> updateModule(@PathVariable Long id, @RequestBody Module module) {
//        return ResponseEntity.ok(moduleService.updateModule(id, module));
//    }
//
//    @DeleteMapping("/{id}")
//    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
//    public ResponseEntity<Void> deleteModule(@PathVariable Long id) {
//        moduleService.deleteModule(id);
//        return ResponseEntity.noContent().build();
//    }
//}