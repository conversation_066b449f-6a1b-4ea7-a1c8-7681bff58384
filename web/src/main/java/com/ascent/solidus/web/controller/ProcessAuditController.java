package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.domain.module.ProcessAudit;
import com.ascent.solidus.services.ProcessAuditService;
import io.swagger.v3.oas.annotations.Parameter;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/processaudit")
public class ProcessAuditController {
  private final ProcessAuditService service;

  public ProcessAuditController(ProcessAuditService service) {
    this.service = service;
  }

  @GetMapping
  public ResponseEntity<Page> getAll(
      @RequestParam @Parameter(required = false) Map<String, Object> filters, Pageable pageable) {
    return ResponseEntity.ok(service.findAll(filters, pageable));
  }

  @GetMapping("/{id}")
  public ProcessAudit getById(@PathVariable("id") String id) {
    return service.findById(id);
  }

  @PostMapping
  public ProcessAudit create(@RequestBody ProcessAudit entity) {
    return service.save(entity);
  }

  @PutMapping("/{id}")
  public ProcessAudit update(@PathVariable("id") String id, @RequestBody ProcessAudit entity) {
    entity.setId(id);
    return service.save(entity);
  }

  @DeleteMapping("/{id}")
  public void delete(@PathVariable("id") String id) {
    service.delete(id);
  }
}
