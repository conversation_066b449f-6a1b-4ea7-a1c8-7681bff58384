<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.2.3</version>
		<relativePath/>
	</parent>
	<groupId>com.ascent.solidus</groupId>
	<artifactId>solidus-platform</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>solidus-platform</name>
	<description>Demo project for Spring Boot</description>
	<packaging>pom</packaging>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>21</java.version>
	</properties>

	<modules>
		<module>core</module>
		<module>services</module>
		<module>web</module>
		<module>batch</module>
	</modules>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.ascent.solidus</groupId>
				<artifactId>core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.ascent.solidus</groupId>
				<artifactId>services</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-data-jpa</artifactId>
			</dependency>

			<dependency>
				<groupId>org.postgresql</groupId>
				<artifactId>postgresql</artifactId>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-jdbc</artifactId>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
<!--					<configuration>-->
<!--						<excludes>-->
<!--							<exclude>-->
<!--								<groupId>org.projectlombok</groupId>-->
<!--								<artifactId>lombok</artifactId>-->
<!--							</exclude>-->
<!--						</excludes>-->
<!--					</configuration>-->
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

</project>
