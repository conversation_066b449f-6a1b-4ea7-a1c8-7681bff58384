#!/bin/bash

# Set base URL
BASE_URL="http://localhost:8080"
JWT_TOKEN="your-jwt-token-here"

# 1. Create a standalone module (parent)
echo "Creating standalone module..."
curl -X POST \
  "${BASE_URL}/modules/standalone" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Product Management",
    "description": "Module for managing products",
    "isStandalone": true,
    "icon": "product_icon"
  }'

echo -e "\n\n"

# 2. Get all standalone modules
echo "Getting all standalone modules..."
curl -X GET \
  "${BASE_URL}/modules/standalone" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 3. Create a component module (child)
echo "Creating component module..."
curl -X POST \
  "${BASE_URL}/modules/component" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Products",
    "description": "Manage individual products",
    "isComponent": true,
    "parent": {
      "id": 1
    },
    "icon": "product_list_icon"
  }'

echo -e "\n\n"

# 4. Get module by ID
echo "Getting module by ID..."
curl -X GET \
  "${BASE_URL}/modules/1" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 5. Get module by name
echo "Getting module by name..."
curl -X GET \
  "${BASE_URL}/modules/name/Products" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 6. Get submodules of a parent module
echo "Getting submodules..."
curl -X GET \
  "${BASE_URL}/modules/1/submodules" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 7. Update a module
echo "Updating module..."
curl -X PUT \
  "${BASE_URL}/modules/2" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Products",
    "description": "Updated description for Products module",
    "icon": "updated_product_icon"
  }'

echo -e "\n\n"

# 8. Get all modules
echo "Getting all modules..."
curl -X GET \
  "${BASE_URL}/modules" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 9. Get all component modules
echo "Getting all component modules..."
curl -X GET \
  "${BASE_URL}/modules/component" \
  -H "Authorization: Bearer ${JWT_TOKEN}"

echo -e "\n\n"

# 10. Delete a module (uncomment if needed)
# echo "Deleting module..."
# curl -X DELETE \
#   "${BASE_URL}/modules/2" \
#   -H "Authorization: Bearer ${JWT_TOKEN}"