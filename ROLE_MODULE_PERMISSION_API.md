# Role-Module Permission API Guide

This API supports the role-based module permission UI shown in your image.

## Base URL
```
/api/role-module-permissions
```

## 1. **Get Available Roles** (for dropdown)

```http
GET /api/role-module-permissions/roles
```

**Response:**
```json
[
  "ROLE_SUPER_ADMIN",
  "ROLE_ORGANIZATION_ADMIN", 
  "ROLE_FACTORY_ADMIN",
  "ROLE_SUPERVISOR",
  "ROLE_INSPECTOR",
  "ROLE_USER",
  "ROLE_SUPPLIER"
]
```

## 2. **Get Module Structure** (for tree display)

```http
GET /api/role-module-permissions/modules/structure
```

**Response:**
```json
[
  {
    "moduleName": "Product_Management",
    "moduleId": "123",
    "isParentModule": true,
    "description": "Product Management module",
    "subModules": [
      {
        "moduleName": "ProductFamily",
        "moduleId": "ProductFamily_001",
        "isParentModule": false,
        "parentModuleName": "Product_Management",
        "description": "ProductFamily description"
      },
      {
        "moduleName": "Revision",
        "moduleId": "revision_001", 
        "isParentModule": false,
        "parentModuleName": "Product_Management",
        "description": "Revision description"
      }
    ]
  },
  {
    "moduleName": "CTQ",
    "moduleId": "456",
    "isParentModule": true,
    "description": "CTQ module",
    "subModules": [
      {
        "moduleName": "Part",
        "moduleId": "Part_001",
        "isParentModule": false,
        "parentModuleName": "CTQ",
        "description": "Part description"
      }
    ]
  }
]
```

## 3. **Get Role Permissions** (for loading checkboxes)

```http
GET /api/role-module-permissions/roles/{roleName}/permissions
```

**Example:**
```http
GET /api/role-module-permissions/roles/ROLE_SUPERVISOR/permissions
```

**Response:**
```json
[
  {
    "id": 1,
    "roleName": "ROLE_SUPERVISOR",
    "moduleName": "Product_Management",
    "parentModuleName": null,
    "isParentModule": true,
    "canRead": true,
    "canWrite": true,
    "canDelete": false,
    "canExecute": true,
    "active": true
  },
  {
    "id": 2,
    "roleName": "ROLE_SUPERVISOR", 
    "moduleName": "ProductFamily",
    "parentModuleName": "Product_Management",
    "isParentModule": false,
    "canRead": true,
    "canWrite": true,
    "canDelete": false,
    "canExecute": false,
    "active": true
  }
]
```

## 4. **Save Role Permissions** (Save button)

```http
POST /api/role-module-permissions/roles/{roleName}/assign
```

**Request Body:**
```json
[
  {
    "moduleName": "Product_Management",
    "parentModuleName": null,
    "isParentModule": true,
    "canRead": true,
    "canWrite": true,
    "canDelete": false,
    "canExecute": true
  },
  {
    "moduleName": "ProductFamily",
    "parentModuleName": "Product_Management", 
    "isParentModule": false,
    "canRead": true,
    "canWrite": true,
    "canDelete": false,
    "canExecute": false
  },
  {
    "moduleName": "Revision",
    "parentModuleName": "Product_Management",
    "isParentModule": false,
    "canRead": true,
    "canWrite": false,
    "canDelete": false,
    "canExecute": false
  }
]
```

**Response:**
```json
"Modules assigned successfully to role: ROLE_SUPERVISOR"
```

## 5. **Check Access** (for runtime permission checking)

```http
GET /api/role-module-permissions/roles/{roleName}/modules/{moduleName}/access
```

**Example:**
```http
GET /api/role-module-permissions/roles/ROLE_SUPERVISOR/modules/ProductFamily/access
```

**Response:**
```json
true
```

## Frontend Implementation Guide

### 1. **Loading the UI**

```javascript
// 1. Load available roles for dropdown
const roles = await fetch('/api/role-module-permissions/roles').then(r => r.json());

// 2. Load module structure for tree
const moduleStructure = await fetch('/api/role-module-permissions/modules/structure').then(r => r.json());

// 3. When role is selected, load existing permissions
const selectedRole = 'ROLE_SUPERVISOR';
const permissions = await fetch(`/api/role-module-permissions/roles/${selectedRole}/permissions`).then(r => r.json());
```

### 2. **Building the Tree UI**

```javascript
function buildModuleTree(moduleStructure, permissions) {
  return moduleStructure.map(parentModule => ({
    name: parentModule.moduleName,
    checked: isModuleChecked(parentModule.moduleName, permissions),
    children: parentModule.subModules.map(subModule => ({
      name: subModule.moduleName,
      checked: isModuleChecked(subModule.moduleName, permissions),
      permissions: getModulePermissions(subModule.moduleName, permissions)
    }))
  }));
}

function isModuleChecked(moduleName, permissions) {
  return permissions.some(p => p.moduleName === moduleName && p.active);
}

function getModulePermissions(moduleName, permissions) {
  const perm = permissions.find(p => p.moduleName === moduleName);
  return perm ? {
    canRead: perm.canRead,
    canWrite: perm.canWrite,
    canDelete: perm.canDelete,
    canExecute: perm.canExecute
  } : { canRead: false, canWrite: false, canDelete: false, canExecute: false };
}
```

### 3. **Saving Permissions**

```javascript
function savePermissions(roleName, checkedModules) {
  const permissionRequests = checkedModules.map(module => ({
    moduleName: module.name,
    parentModuleName: module.parentModuleName,
    isParentModule: module.isParentModule,
    canRead: module.permissions.canRead,
    canWrite: module.permissions.canWrite,
    canDelete: module.permissions.canDelete,
    canExecute: module.permissions.canExecute
  }));

  return fetch(`/api/role-module-permissions/roles/${roleName}/assign`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(permissionRequests)
  });
}
```

## Database Schema

The system creates these tables:

```sql
-- Main permissions table
CREATE TABLE role_module_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    parent_module_name VARCHAR(100),
    is_parent_module BOOLEAN NOT NULL DEFAULT FALSE,
    can_read BOOLEAN NOT NULL DEFAULT TRUE,
    can_write BOOLEAN NOT NULL DEFAULT FALSE,
    can_delete BOOLEAN NOT NULL DEFAULT FALSE,
    can_execute BOOLEAN NOT NULL DEFAULT FALSE,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_role_module (role_name, module_name)
);
```

This system provides exactly what your UI needs - role-based module permissions with hierarchical structure and granular permission controls!
